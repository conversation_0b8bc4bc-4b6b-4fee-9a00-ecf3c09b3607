-- MySQL Schema for Reminders and Todo Lists

-- Reminders Table
CREATE TABLE reminders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    reminder_date DATETIME NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_is_completed (is_completed)
);

-- Todo Lists Table
CREATE TABLE todo_lists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id)
);

-- Todo Items Table
CREATE TABLE todo_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    list_id INT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    due_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (list_id) REFERENCES todo_lists(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_list_id (list_id),
    INDEX idx_is_completed (is_completed),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date)
);

-- Sample data (optional)
INSERT INTO todo_lists (user_id, name, description) VALUES 
(1, 'Personal Tasks', 'My personal todo items'),
(1, 'Work Tasks', 'Work related tasks');

INSERT INTO todo_items (user_id, list_id, title, description, priority, due_date) VALUES 
(1, 1, 'Buy groceries', 'Milk, bread, eggs', 'medium', '2025-01-15 18:00:00'),
(1, 1, 'Call dentist', 'Schedule appointment', 'high', '2025-01-14 10:00:00'),
(1, 2, 'Finish project report', 'Complete quarterly report', 'high', '2025-01-16 17:00:00');

INSERT INTO reminders (user_id, title, description, reminder_date) VALUES 
(1, 'Meeting with client', 'Discuss project requirements', '2025-01-15 14:00:00'),
(1, 'Take medicine', 'Daily vitamin', '2025-01-14 08:00:00');
