-- MySQL Schema for AI Assistant with Reminders and Todo Lists
-- Run these commands in your MySQL database

-- Create database (optional, if not exists)
-- CREATE DATABASE IF NOT EXISTS nityasha_ai;
-- USE nityasha_ai;

-- 1. Create Reminders Table
CREATE TABLE IF NOT EXISTS reminders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    reminder_date DATETIME NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for better performance
    INDEX idx_user_id (user_id),
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_is_completed (is_completed),
    INDEX idx_user_completed (user_id, is_completed)
);

-- 2. Create Todo Lists Table
CREATE TABLE IF NOT EXISTS todo_lists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for better performance
    INDEX idx_user_id (user_id)
);

-- 3. Create Todo Items Table
CREATE TABLE IF NOT EXISTS todo_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    list_id INT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    due_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (list_id) REFERENCES todo_lists(id) ON DELETE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_user_id (user_id),
    INDEX idx_list_id (list_id),
    INDEX idx_is_completed (is_completed),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date),
    INDEX idx_user_completed (user_id, is_completed),
    INDEX idx_user_list (user_id, list_id)
);

-- 4. Insert Sample Data (Optional - for testing)
-- Sample Todo Lists
INSERT INTO todo_lists (user_id, name, description) VALUES 
(12, 'Personal Tasks', 'My personal todo items'),
(12, 'Work Tasks', 'Work related tasks'),
(12, 'Shopping List', 'Items to buy');

-- Sample Todo Items
INSERT INTO todo_items (user_id, list_id, title, description, priority, due_date) VALUES 
(12, 1, 'Buy groceries', 'Milk, bread, eggs', 'medium', '2025-01-15 18:00:00'),
(12, 1, 'Call dentist', 'Schedule appointment', 'high', '2025-01-14 10:00:00'),
(12, 2, 'Finish project report', 'Complete quarterly report', 'high', '2025-01-16 17:00:00'),
(12, 3, 'Buy milk', 'Fresh milk from store', 'low', NULL),
(12, 3, 'Buy bread', 'Whole wheat bread', 'low', NULL);

-- Sample Reminders
INSERT INTO reminders (user_id, title, description, reminder_date) VALUES 
(12, 'Meeting with client', 'Discuss project requirements', '2025-01-15 14:00:00'),
(12, 'Take medicine', 'Daily vitamin', '2025-01-14 08:00:00'),
(12, 'Call doctor', 'Schedule checkup appointment', '2025-01-16 10:00:00');

-- 5. Verify tables were created
SHOW TABLES;

-- 6. Check table structures
DESCRIBE reminders;
DESCRIBE todo_lists;
DESCRIBE todo_items;

-- 7. Test queries to verify data
SELECT 'Reminders for user 12:' as info;
SELECT * FROM reminders WHERE user_id = 12;

SELECT 'Todo Lists for user 12:' as info;
SELECT * FROM todo_lists WHERE user_id = 12;

SELECT 'Todo Items for user 12:' as info;
SELECT ti.*, tl.name as list_name 
FROM todo_items ti 
LEFT JOIN todo_lists tl ON ti.list_id = tl.id 
WHERE ti.user_id = 12;
