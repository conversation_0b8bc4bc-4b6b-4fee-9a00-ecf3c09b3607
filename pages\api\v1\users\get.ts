import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  let connection;
  try {
    connection = await connectToDatabase();

    const baseUrl = 'http://status.api.nityasha.com/uploads/';

    const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(`
      SELECT 
        u.*, 
        s.id AS status_id,
        s.user_id AS status_user_id,
        s.media_url AS status_media_url,
        s.created_at AS status_created_at
      FROM users u
      LEFT JOIN statuses s 
        ON u.id = s.user_id AND s.created_at >= NOW() - INTERVAL 1 DAY
    `);

    if (rows.length === 0) {
      return res.status(404).json({ message: 'No users found' });
    }

    const usersMap = new Map();

    for (const row of rows) {
      const userId = row.id;

      if (!usersMap.has(userId)) {
        // Copy all user fields dynamically
        const user = {};
        for (const key in row) {
          if (!key.startsWith('status_')) {
            user[key] = row[key];
          }
        }
        user.statuses = [];
        usersMap.set(userId, user);
      }

      if (row.status_id) {
        usersMap.get(userId).statuses.push({
          id: row.status_id,
          user_id: row.status_user_id,
          media_url: baseUrl + row.status_media_url.split('/').pop(),
          created_at: row.status_created_at,
        });
      }
    }

    res.status(200).json(Array.from(usersMap.values()));
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to fetch users with statuses' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
