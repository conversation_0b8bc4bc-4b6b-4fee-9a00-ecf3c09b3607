import mysql from 'mysql2/promise';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Extract userId and consultantId from the request body
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { userId, consultantId, sessionId, chatDuration, perMinuteRate } = req.body;

  const totalCost = chatDuration * perMinuteRate;

  const connection = await mysql.createConnection({
    host: process.env.MYSQL_HOST,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
  });

  try {
    // Update the session with the chat duration and total cost
    await connection.execute(
      'UPDATE chat_sessions SET total_cost = ?, chat_duration = ? WHERE id = ?',
      [totalCost, chatDuration, sessionId]
    );

    res.status(200).json({ message: 'Chat session ended successfully', totalCost });
  } catch (error) {
    console.error(error); // Log the error to avoid the ESLint warning
    res.status(500).json({ error: 'Failed to end chat session a' });
  } finally {
    connection.end();
  }
}
