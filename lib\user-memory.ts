import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface UserMemoryInput {
  userId: number;
  key: string;
  value: string;
  description?: string;
}

export class UserMemoryManager {
  /**
   * Store or update a memory for a user
   */
  static async setMemory({ userId, key, value, description }: UserMemoryInput) {
    return prisma.userMemory.upsert({
      where: {
        userId_key: {
          userId,
          key,
        },
      },
      update: {
        value,
        description,
      },
      create: {
        userId,
        key,
        value,
        description,
      },
    });
  }

  /**
   * Retrieve a specific memory for a user
   */
  static async getMemory(userId: number, key: string) {
    return prisma.userMemory.findUnique({
      where: {
        userId_key: {
          userId,
          key,
        },
      },
    });
  }

  /**
   * Get all memories for a user
   */
  static async getAllMemories(userId: number) {
    return prisma.userMemory.findMany({
      where: {
        userId,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });
  }

  /**
   * Delete a specific memory
   */
  static async deleteMemory(userId: number, key: string) {
    return prisma.userMemory.delete({
      where: {
        userId_key: {
          userId,
          key,
        },
      },
    });
  }

  /**
   * Delete all memories for a user
   */
  static async deleteAllMemories(userId: number) {
    return prisma.userMemory.deleteMany({
      where: {
        userId,
      },
    });
  }
}
