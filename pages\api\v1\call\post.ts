import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { consantent_id, customer_id, room_id, Status, timer, type } = req.body;

    // Check for missing required fields
    if (!consantent_id || !customer_id || !room_id || !Status || !timer || !type) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    try {
        // Insert the new call into the `Calls` table
        const result = await query(
            'INSERT INTO Calls (consantent_id, customer_id, room_id, Status, timer, type) VALUES (?, ?, ?, ?, ?, ?)',
            [consantent_id, customer_id, room_id, Status, timer, type]
        );

        return res.status(201).json({ message: 'Call created successfully', result });
    } catch (error) {
        console.error('Database query failed:', error);
        return res.status(500).json({ message: 'Internal server error', error });
    }
}
