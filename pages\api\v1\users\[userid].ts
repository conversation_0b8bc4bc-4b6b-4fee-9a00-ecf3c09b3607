import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket, ResultSetHeader } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    // Handle GET request
    if (req.method === 'GET') {
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query('SELECT * FROM users WHERE id = ?', [userid]);

      if (rows.length === 0) {
        return res.status(404).json({ message: 'User not found' });
      }

      return res.status(200).json(rows[0]);
    }

    // Handle PUT request
    if (req.method === 'PUT') {
      const {
        balance,  // Ensure balance is included for updates
        gender,
        total_request,
        status,
        profile_url,
        contact_no,
        birth_date,
        birth_time,
        total_sales,
        isLive,
        pfp,
        role,
        username,
      } = req.body;

      // Construct the update query with the fields provided
      const fieldsToUpdate = [];
      const values = [];

      // Add fields only if they are defined or explicitly null
      if (balance !== undefined) {
        fieldsToUpdate.push('balance = ?'); // Adding balance to update
        values.push(balance);
      }
      if (gender !== undefined) {
        fieldsToUpdate.push('gender = ?');
        values.push(gender);
      }
      if (total_request !== undefined) {
        fieldsToUpdate.push('total_request = ?');
        values.push(total_request);
      }
      if (status !== undefined) {
        fieldsToUpdate.push('status = ?');
        values.push(status);
      }
      if (profile_url !== undefined) {
        fieldsToUpdate.push('profile_url = ?');
        values.push(profile_url);
      }
      if (contact_no !== undefined) {
        fieldsToUpdate.push('contact_no = ?');
        values.push(contact_no);
      }
      if (birth_date !== undefined) {
        fieldsToUpdate.push('birth_date = ?');
        values.push(birth_date);
      }
      if (birth_time !== undefined) {
        fieldsToUpdate.push('birth_time = ?');
        values.push(birth_time);
      }
      if (total_sales !== undefined) {
        fieldsToUpdate.push('total_sales = ?');
        values.push(total_sales);
      }
      if (isLive !== undefined) {
        fieldsToUpdate.push('isLive = ?');
        values.push(isLive);
      }
      if (pfp !== undefined) {
        fieldsToUpdate.push('pfp = ?');
        values.push(pfp);
      }
      if (role !== undefined) {
        fieldsToUpdate.push('role = ?');
        values.push(role);
      }
      if (username !== undefined) {
        fieldsToUpdate.push('username = ?');
        values.push(username);
      }

      // Add userid to the values for the WHERE clause
      values.push(userid);

      // Execute the update query
      if (fieldsToUpdate.length > 0) {
        const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
          `UPDATE users SET ${fieldsToUpdate.join(', ')} WHERE id = ?`,
          values
        );

        if (result.affectedRows === 0) {
          return res.status(404).json({ message: 'User not found or not updated' });
        }

        return res.status(200).json({ message: 'User updated successfully' });
      } else {
        return res.status(400).json({ error: 'No fields to update' });
      }
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
