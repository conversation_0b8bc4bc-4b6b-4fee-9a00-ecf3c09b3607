import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db'; 
import bcrypt from 'bcrypt';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';


interface User extends RowDataPacket { 
    id: number;          
    email: string;      
    password: string;  
}

const loginHandler = async (req: NextApiRequest, res: NextApiResponse) => {
    if (req.method === 'POST') {
        const { email, password } = req.body;

        // Validate request body
        if (!email || !password) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        try {
            // Connect to the database
            const db = await connectToDatabase();

            // Query the database for the user
            const [rows]: [User[], FieldPacket[]] = await db.query<User[]>('SELECT * FROM consultants WHERE email = ?', [email]);

            // Extract the first user from the results
            const user = rows[0];

            // If no user is found, return an error
            if (!user) {
                return res.status(401).json({ message: 'Invalid credentials' });
            }

            // Compare the provided password with the stored hashed password
            const isPasswordValid = await bcrypt.compare(password, user.password);
            if (!isPasswordValid) {
                return res.status(401).json({ message: 'Invalid credentials' });
            }

            // Respond with success and user ID (or any other relevant info)
            return res.status(200).json({ message: 'Login successful', userId: user.id });
        } catch (error) {
            console.error('Error logging in:', error);
            return res.status(500).json({ message: 'Error logging in' });
        }
    } else {
        // Handle unsupported HTTP methods
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
};

export default loginHandler;
