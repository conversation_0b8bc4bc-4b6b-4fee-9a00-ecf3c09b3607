import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket, ResultSetHeader } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    // Handle GET request
    if (req.method === 'GET') {
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT * FROM Widowable WHERE user_id = ?',
        [userid]
      );

      if (rows.length === 0) {
        return res.status(404).json([]);
      }

      return res.status(200).json(rows); // Return all matching records
    }

    // Handle POST request
    if (req.method === 'POST') {
      const { money, widow_status, widow_date } = req.body;

      if (!money || !widow_status || !widow_date) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
        'INSERT INTO Widowable (user_id, money, status, widow_date) VALUES (?, ?, ?, ?)',
        [userid, money, widow_status, widow_date]
      );

      return res.status(201).json({ message: 'Widowable record created successfully', id: result.insertId });
    }

    // Handle PUT request
    if (req.method === 'PUT') {
      const { money, widow_status, widow_date } = req.body;

      const widowableFieldsToUpdate = [];
      const widowableValues = [];

      if (money !== undefined) {
        widowableFieldsToUpdate.push('money = ?');
        widowableValues.push(money);
      }
      if (widow_status !== undefined) {
        widowableFieldsToUpdate.push('status = ?');
        widowableValues.push(widow_status);
      }
      if (widow_date !== undefined) {
        widowableFieldsToUpdate.push('widow_date = ?');
        widowableValues.push(widow_date);
      }

      widowableValues.push(userid);

      if (widowableFieldsToUpdate.length > 0) {
        const [widowableResult]: [ResultSetHeader, FieldPacket[]] = await connection.query(
          `UPDATE Widowable SET ${widowableFieldsToUpdate.join(', ')} WHERE user_id = ?`,
          widowableValues
        );

        if (widowableResult.affectedRows === 0) {
          return res.status(404).json({ message: 'Widowable record not found or not updated' });
        }

        return res.status(200).json({ message: 'Widowable data updated successfully' });
      } else {
        return res.status(400).json({ error: 'No fields to update in Widowable' });
      }
    }

    // Handle DELETE request
    if (req.method === 'DELETE') {
      const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
        'DELETE FROM Widowable WHERE user_id = ?',
        [userid]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'Widowable record not found' });
      }

      return res.status(200).json({ message: 'Widowable record deleted successfully' });
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
