import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const { id } = req.query; // Extract the call ID from the URL

    // Check if id is provided and is a string
    if (!id || Array.isArray(id)) {
        return res.status(400).json({ message: 'Missing or invalid call ID in URL' });
    }

    if (req.method !== 'PUT') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { consantent_id, customer_id, room_id, Status, timer, type } = req.body;

    // Check if at least one field is provided to update
    if (!consantent_id && !customer_id && !room_id && !Status && !timer && !type) {
        return res.status(400).json({ message: 'No fields to update' });
    }

    try {
        // Prepare the dynamic update query
        const updateFields: string[] = [];
        const updateValues: (string | number | null)[] = []; // Specify more specific types

        if (consantent_id) {
            updateFields.push('consantent_id = ?');
            updateValues.push(consantent_id);
        }
        if (customer_id) {
            updateFields.push('customer_id = ?');
            updateValues.push(customer_id);
        }
        if (room_id) {
            updateFields.push('room_id = ?');
            updateValues.push(room_id);
        }
        if (Status) {
            updateFields.push('Status = ?');
            updateValues.push(Status);
        }
        if (timer) {
            updateFields.push('timer = ?');
            updateValues.push(timer);
        }
        if (type) {
            updateFields.push('type = ?');
            updateValues.push(type);
        }

        updateValues.push(id); // Add the `id` for the WHERE clause at the end

        // Perform the update query with the correct column name (e.g., 'id')
        const result = await query(
            `UPDATE Calls SET ${updateFields.join(', ')} WHERE id = ?`, // Assuming the column is `id`
            updateValues
        );

        return res.status(200).json({ message: 'Call updated successfully', result });
    } catch (error) {
        console.error('Database query failed:', error);
        return res.status(500).json({ message: 'Internal server error', error });
    }
}
