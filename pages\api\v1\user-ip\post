import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';
import { OkPacket } from 'mysql2'; // Adjust this import based on your actual MySQL library

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { ip } = req.body;

        // Valate input
        if (!ip ) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        try {
            // Insert the report into the database
            const result = await query(
                'INSERT INTO ips (ip) VALUES (?, ?, ?, ?, 0, NOW())',
                [ ip ]
            );

            // Check the type of result and cast it
            const insertResult = result as OkPacket; // Assert the result type


            return res.status(201).json({
                message: 'Report successfully created',
            });
        } catch (error) {
            console.error('Error creating report:', error);
            return res.status(500).json({ message: 'Server error' });
        }
    } else {
        // Handle other HTTP methods
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
