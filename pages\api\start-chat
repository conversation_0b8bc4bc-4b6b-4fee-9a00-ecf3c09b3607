import mysql from 'mysql2/promise';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { userId, consultantId } = req.body;

  const connection = await mysql.createConnection({
    host: process.env.MYSQL_HOST,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
  });

  try {
    const [result] = await connection.execute(
      'INSERT INTO chat_sessions (user_id, consultant_id, total_cost, chat_duration) VALUES (?, ?, 0, 0)',
      [userId, consultantId]
    );

    res.status(200).json({ sessionId: result.insertId });
  } catch (error) {
    console.error(error); // Log the error to avoid the warning
    res.status(500).json({ error: 'Failed to start chat session a' });
  }
   finally {
    connection.end();
  }
}
