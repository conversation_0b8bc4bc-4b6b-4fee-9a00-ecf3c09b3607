import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db'; // Adjust the path according to your project structure

export async function GET(req: NextApiRequest, { params }: { params: { id: string } }) {
    const { id } = params;

    try {
        const sql = 'SELECT * FROM streams WHERE id = ?';
        const stream = await query(sql, [id]);

        if (stream.length === 0) {
            return new Response(JSON.stringify({ message: 'Stream not found' }), { status: 404 });
        }

        return new Response(JSON.stringify(stream[0]), { status: 200 });
    } catch (error: unknown) {
        return new Response(JSON.stringify({ message: 'Failed to retrieve stream', error: (error as Error).message }), { status: 500 });
    }
}
