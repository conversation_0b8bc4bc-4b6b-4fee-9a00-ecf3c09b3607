import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const { id } = req.query; // Extract the gift ID from the URL

    // Check if id is provided and is a string
    if (!id || Array.isArray(id)) {
        return res.status(400).json({ message: 'Missing or invalid gift ID in URL' });
    }

    if (req.method !== 'PUT') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { name, icon, amount, status, user_id, consantent_id } = req.body;

    // Check if at least one field is provided to update
    if (!name && !icon && !amount && !status && !user_id && !consantent_id) {
        return res.status(400).json({ message: 'No fields to update' });
    }

    try {
        // Prepare the dynamic update query
        const updateFields: string[] = [];
        const updateValues: (string | number | null)[] = []; // Specify more specific types

        if (name) {
            updateFields.push('name = ?');
            updateValues.push(name);
        }
        if (icon) {
            updateFields.push('icon = ?');
            updateValues.push(icon);
        }
        if (amount) {
            updateFields.push('amount = ?');
            updateValues.push(amount);
        }
        if (status) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }
        if (user_id) {
            updateFields.push('user_id = ?');
            updateValues.push(user_id);
        }
        if (consantent_id) {
            updateFields.push('consantent_id = ?');
            updateValues.push(consantent_id);
        }

        updateValues.push(id); // Add the `id` for the WHERE clause at the end

        // Perform the update query with the correct column name (e.g., 'id')
        const result = await query(
            `UPDATE gifts SET ${updateFields.join(', ')} WHERE id = ?`, // Assuming the column is `id`
            updateValues
        );

        return res.status(200).json({ message: 'Gift updated successfully', result });
    } catch (error) {
        console.error('Database query failed:', error);
        return res.status(500).json({ message: 'Internal server error', error });
    }
}
