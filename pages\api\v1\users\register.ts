import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db'; // Adjust this import based on your DB connection logic
import { FieldPacket, ResultSetHeader } from 'mysql2/promise';
import bcrypt from 'bcrypt';

const registerHandler = async (req: NextApiRequest, res: NextApiResponse) => {
    if (req.method === 'POST') {
        const { email, password, username } = req.body;

        // Validate request body
        if (!email || !password || !username) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        try {
            const db = await connectToDatabase();

            // Hash the password before storing it
            const hashedPassword = await bcrypt.hash(password, 10); // Adjust the salt rounds as needed

            // Perform the insert query, assuming the users table requires email, password, and username
            const [result]: [ResultSetHeader, FieldPacket[]] = await db.query(
                'INSERT INTO users (email, password, username) VALUES (?, ?, ?)',
                [email, hashedPassword, username]
            );

            // Return the success response with the user ID
            return res.status(201).json({ message: 'User registered successfully', userId: result.insertId });
        } catch (error) {
            console.error('Error creating user:', error);
            return res.status(500).json({ message: 'Error creating user' });
        }
    } else {
        // Handle unsupported HTTP methods
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
};

export default registerHandler;
