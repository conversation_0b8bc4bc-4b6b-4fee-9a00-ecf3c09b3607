import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check if the request method is GET
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const { id } = req.query; // Get the user ID from the query parameters

  // Ensure the ID is provided
  if (!id) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  const userId = Array.isArray(id) ? id[0] : id; // Handle array case
  console.log('User ID:', userId); // Log the user ID

  let connection;
  try {
    // Create a database connection
    connection = await connectToDatabase();
    console.log('Database connected successfully'); // Log connection

    // Execute the query to fetch the chat request with the highest ID for the given user ID
    console.log('Executing query for user ID:', userId); // Log the query context
    const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
      'SELECT * FROM chatRequests WHERE userId = ? ORDER BY id DESC LIMIT 1',
      [userId]
    );
    console.log('Query result:', rows); // Log the returned rows

    // Check if no chat requests are found
    if (Array.isArray(rows) && rows.length === 0) {
      return res.status(404).json({ message: 'No chat requests found for this user' });
    }

    // Format the response
    const response = rows.map(row => ({
      id: row.id,
      userId: row.userId,
      consultantId: row.consultantId,
      status: row.status,
      bel: row.bel,
      time: row.time,
      RoomN: row.RoomN
    }));

    // Send the result back as JSON
    res.status(200).json(response); // Return the formatted response
  } catch (error) {
    // Log the error and send a 500 response
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to fetch chat request' });
  } finally {
    // Ensure the connection is closed
    if (connection) {
      await connection.end();
    }
  }
}
