import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

interface ChatRequest extends RowDataPacket {
    id: number;
    userId: string;
    consultantId: string | null;
    status: string | null;
    bel: string | null;
    time: string | null;
    RoomN: string | null;
}

interface Consultant extends RowDataPacket {
    id: number;
    name: string | null;
    per_minute_rate: number | null;
    birth_date: string | null;
    total_sales: number | null;
    isLive: boolean | null;
    password: string | null;
    pfp: string | null;
    TOTAL_REQUEST: number | null;
    GENDER: string | null;
    profile_url: string | null;
    categories: string | null;
    auth_check: boolean | null;
    code: string | null;
    number: string | null;
    email: string | null;
    balance: number | null;
    verified_email: boolean | null;
    categoriess: string | null;
    exp: number | null;
    isChatOn: boolean | null;
    thumnel: string | null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method Not Allowed' });
    }

    const { usersid } = req.query;
    if (!usersid) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    const userId = Array.isArray(usersid) ? usersid[0] : usersid;
    let connection;

    try {
        connection = await connectToDatabase();
        const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
            `
            SELECT cr.*, c.*
            FROM chatRequests cr
            JOIN consultants c ON cr.consultantId = c.id
            WHERE cr.userId = ?
            ORDER BY cr.id DESC
            `,
            [userId]
        );

        if (Array.isArray(rows) && rows.length === 0) {
            return res.status(404).json({ message: 'No chat requests found for this user' });
        }

        const response = rows.map((row): { chatRequest: ChatRequest; consultant: Consultant } => {
            // Cast the row to 'ChatRequest & Consultant'
            const chatRequest = {
                id: Number(row.id),
                userId: String(row.userId),
                consultantId: row.consultantId !== undefined ? String(row.consultantId) : null,
                status: row.status ?? null,
                bel: row.bel ?? null,
                time: row.time ?? null,
                RoomN: row.RoomN ?? null,
            } as ChatRequest; // Assert type here

            const consultant = {
                id: Number(row.consultantId),
                name: row.name ?? null,
                per_minute_rate: row.per_minute_rate !== undefined ? Number(row.per_minute_rate) : null,
                birth_date: row.birth_date ?? null,
                total_sales: row.total_sales !== undefined ? Number(row.total_sales) : null,
                isLive: row.isLive ?? null,
                password: row.password ?? null,
                pfp: row.pfp ?? null,
                TOTAL_REQUEST: row.TOTAL_REQUEST !== undefined ? Number(row.TOTAL_REQUEST) : null,
                GENDER: row.GENDER ?? null,
                profile_url: row.profile_url ?? null,
                categories: row.categories ?? null,
                auth_check: row.auth_check ?? null,
                code: row.code ?? null,
                number: row.number ?? null,
                email: row.email ?? null,
                balance: row.balance !== undefined ? Number(row.balance) : null,
                verified_email: row.verified_email ?? null,
                categoriess: row.categoriess ?? null,
                exp: row.exp !== undefined ? Number(row.exp) : null,
                isChatOn: row.isChatOn ?? null,
                thumnel: row.thumnel ?? null,
            } as Consultant; // Assert type here

            return { chatRequest, consultant };
        });

        res.status(200).json(response);
    } catch (error: unknown) {
        console.error('Database query failed:', error);
        if (error instanceof Error) {
            res.status(500).json({ error: `Failed to fetch chat requests: ${error.message}` });
        } else {
            res.status(500).json({ error: 'Failed to fetch chat requests: An unknown error occurred.' });
        }
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}
