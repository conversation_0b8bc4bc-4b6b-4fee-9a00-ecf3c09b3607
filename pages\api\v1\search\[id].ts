import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check if the request method is GET
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const { id } = req.query; // Get the user ID from the query parameters

  // Ensure the ID is provided
  if (!id) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  const userId = Array.isArray(id) ? id[0] : id; // Handle array case
  console.log('User ID:', userId); // Log the user ID

  let connection;
  try {
    // Create a database connection
    connection = await connectToDatabase();
    console.log('Database connected successfully'); // Log connection

    // Query to fetch the chat request with the highest ID for the given user ID
    console.log('Executing chat request query for user ID:', userId);
    const [chatRows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
      'SELECT * FROM chatRequests WHERE userId = ? ORDER BY id DESC LIMIT 1',
      [userId]
    );
    console.log('Chat request query result:', chatRows);

    // Check if no chat requests are found
    if (Array.isArray(chatRows) && chatRows.length === 0) {
      return res.status(404).json({ message: 'No chat requests found for this user' });
    }

    // Get consultantId from the chat request
    const chatRequest = chatRows[0];
    const consultantId = chatRequest.consultantId;

    // Query to fetch consultant details from the users table
    console.log('Executing consultant details query for consultant ID:', consultantId);
    const [consultantRows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
      'SELECT id, birth_date, total_sales, isLive, pfp, GENDER, profile_url, categories, number, email, verified_email, exp, isChatOn, thumnel FROM users WHERE id = ? LIMIT 1',
      [consultantId]
    );
    console.log('Consultant details query result:', consultantRows);

    // Check if no consultant details are found
    if (Array.isArray(consultantRows) && consultantRows.length === 0) {
      return res.status(404).json({ message: 'Consultant details not found' });
    }

    // Format the consultant details into a response
    const consultantDetails = consultantRows[0];

    const response = {
      consultantDetails: {
        id: consultantDetails.id,
        birth_date: consultantDetails.birth_date,
        total_sales: consultantDetails.total_sales,
        isLive: consultantDetails.isLive,
        pfp: consultantDetails.pfp,
        GENDER: consultantDetails.GENDER,
        profile_url: consultantDetails.profile_url,
        categories: consultantDetails.categories,
        number: consultantDetails.number,
        email: consultantDetails.email,
        verified_email: consultantDetails.verified_email,
        exp: consultantDetails.exp,
        isChatOn: consultantDetails.isChatOn,
        thumnel: consultantDetails.thumnel,
      }
    };

    // Send the result back as JSON
    res.status(200).json(response);
  } catch (error) {
    // Log the error and send a 500 response
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to fetch consultant details' });
  } finally {
    // Ensure the connection is closed
    if (connection) {
      await connection.end();
    }
  }
}
