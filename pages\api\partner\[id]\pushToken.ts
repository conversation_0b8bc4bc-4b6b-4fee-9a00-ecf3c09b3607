import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';

type Data = {
  message?: string;
  error?: string;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse<Data>) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { id } = req.query;

  if (!id || isNaN(Number(id))) {
    return res.status(400).json({ error: 'Invalid or missing consultant ID' });
  }

  const { pushToken } = req.body;

  if (!pushToken || typeof pushToken !== 'string' || pushToken.trim() === '') {
    return res.status(400).json({ error: 'Missing or invalid push token' });
  }

  try {
    // Connect to the database
    const connection = await connectToDatabase();

    // Update the push token in the database
    const [result] = await connection.execute(
      'UPDATE consultants SET push_token = ? WHERE id = ?',
      [pushToken, id]
    );

    const affectedRows = (result as any).affectedRows;

    if (affectedRows > 0) {
      return res.status(200).json({ message: 'Push token updated successfully' });
    } else {
      return res.status(404).json({ error: 'Consultant not found' });
    }
  } catch (error: any) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Database error: ' + error.message });
  }
}
