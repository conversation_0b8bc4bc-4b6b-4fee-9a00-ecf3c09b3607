import type { NextApiRequest, NextApiResponse } from 'next';
import multer from 'multer';
import nextConnect from 'next-connect';

// Set up multer for file storage
const storage = multer.diskStorage({
  destination: './public/uploads/', // Directory where files will be saved
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`); // Append timestamp to the filename
  },
});

const upload = multer({ storage });

// Disable Next.js default body parsing to allow for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

declare module 'next' {
  interface NextApiRequest {
    file?: Express.Multer.File; // Extend NextApiRequest with file property
  }
}

// Create the next-connect handler
const apiRoute = nextConnect({
  onError(error, req: NextApiRequest, res: NextApiResponse) {
    res.status(500).json({ error: `Something went wrong! ${error.message}` });
  },
  onNoMatch(req: NextApiRequest, res: NextApiResponse) {
    res.status(405).json({ message: `Method ${req.method} not allowed` });
  },
});

// Middleware to verify token
const verifyToken = (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
  const token = req.headers['authorization'];

  // Simple token check (replace with your actual token validation logic)
  if (!token || token !== 'your-token') { // Replace 'your-token' with your actual token
    return res.status(401).json({ message: 'Unauthorized: Invalid token' });
  }
  next(); // Proceed to the next middleware or handler
};

// Use the verifyToken middleware
apiRoute.use(verifyToken);

// Use multer's middleware to handle file uploads
apiRoute.use(upload.single('file'));

// Define the POST request handler
apiRoute.post((req: NextApiRequest, res: NextApiResponse) => {
  // Handle the uploaded file (available in req.file)
  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  // Send success response
  return res.status(200).json({
    message: 'File uploaded successfully!',
    file: req.file, // Optional: return file details
  });
});

export default apiRoute;
