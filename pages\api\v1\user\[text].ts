import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '../../../../lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const { text } = req.query;
  let connection;

  try {
    connection = await connectToDatabase();
    let query = '';
    let values: any[] = [];

    if (/^\d{10}$/.test(text as string)) {
      query = `
        SELECT id, name, number, email, pfp FROM consultants WHERE number = ?
        UNION
        SELECT id, username AS name, NULL AS number, email, pfp FROM users WHERE email = ?`;
      values = [text, text];
    } else {
      query = `
        SELECT id, name, number, email, pfp FROM consultants WHERE name LIKE ?
        UNION
        SELECT id, username AS name, NULL AS number, email, pfp FROM users WHERE username LIKE ?`;
      values = [`%${text}%`, `%${text}%`];
    }

    const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(query, values);

    if (rows.length === 0) {
      return res.status(404).json({ message: 'No matching records found' });
    }

    res.status(200).json(rows);
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to fetch data' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
