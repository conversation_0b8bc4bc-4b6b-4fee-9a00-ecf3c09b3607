// app/api/chat/route.ts
import { NextRequest } from 'next/server';
import { google } from '@ai-sdk/google';
import { streamText, tool, convertToCoreMessages } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';
import { query } from '@/lib/db';

export const maxDuration = 60;
export const dynamic = 'force-dynamic';

let redis: any = null;

async function getRedisClient() {
  if (!redis) {
    redis = createClient({
      socket: {
        host: 'mercury.nityasha.com',
        port: 26739,
      },
      password: 'Amber@!23',
    });
    redis.on('error', (err: any) => console.error('Redis error', err));
    await redis.connect();
  }
  return redis;
}

function getCurrentISTTime() {
  return new Date().toLocaleString('en-IN', { 
    timeZone: 'Asia/Kolkata',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/(\d{2})\/(\d{2})\/(\d{4}), (\d{2}):(\d{2}):(\d{2})/, '$3-$2-$1 $4:$5:$6');
}

async function checkPendingReminders(userId: number) {
  try {
    const currentTime = getCurrentISTTime();
    const reminders = await query(
      `SELECT * FROM reminders 
       WHERE user_id = ? AND is_completed = 0 AND reminder_date <= ? 
       ORDER BY reminder_date ASC`,
      [userId, currentTime]
    ) as any[];
    return reminders;
  } catch (error) {
    console.error('Error checking pending reminders:', error);
    return [];
  }
}

function createToolsWithUserId(currentUserId: number) {
  return {
    // Weather tool
    get_current_weather: tool({
      description: 'Get current weather for a location',
      parameters: z.object({
        location: z.string().describe('City and region/country'),
        unit: z.enum(['celsius', 'fahrenheit']).optional(),
      }),
      execute: async ({ location, unit = 'celsius' }) => {
        const temperature = unit === 'fahrenheit' ? 86 : 30;
        return { location, unit, temperature, conditions: 'Sunny' };
      },
    }),

    // Google Search tool
    google_search: tool({
      description: 'Search Google for information',
      parameters: z.object({
        query: z.string().describe('Search query'),
      }),
      execute: async ({ query }) => {
        try {
          const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
          const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;
          
          if (!apiKey || !searchEngineId) {
            return { error: 'Google Search API not configured' };
          }

          const response = await fetch(
            `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&num=5`
          );

          if (!response.ok) {
            throw new Error(`Search API error: ${response.status}`);
          }

          const data = await response.json();
          
          return {
            query,
            results: data.items?.slice(0, 5).map((item: any) => ({
              title: item.title,
              snippet: item.snippet,
              link: item.link
            })) || []
          };
        } catch (error) {
          console.error('Google search error:', error);
          return { error: 'Search temporarily unavailable' };
        }
      },
    }),

    // Add Reminder tool
    add_reminder: tool({
      description: 'Add a new reminder for the user',
      parameters: z.object({
        title: z.string().describe('Reminder title'),
        description: z.string().optional().describe('Reminder description'),
        reminder_date: z.string().describe('Reminder date and time in YYYY-MM-DD HH:MM:SS format (IST)'),
      }),
      execute: async ({ title, description, reminder_date }) => {
        try {
          const result = await query(
            'INSERT INTO reminders (user_id, title, description, reminder_date) VALUES (?, ?, ?, ?)',
            [currentUserId, title, description || '', reminder_date]
          ) as any;
          return { success: true, id: result.insertId, message: 'Reminder added successfully' };
        } catch (error) {
          console.error('Add reminder error:', error);
          return { success: false, message: 'Failed to add reminder' };
        }
      },
    }),

    // Get Reminders tool
    get_reminders: tool({
      description: 'Get all reminders for the user',
      parameters: z.object({
        completed: z.boolean().optional().describe('Filter by completion status'),
        upcoming_only: z.boolean().optional().describe('Show only upcoming reminders'),
      }),
      execute: async ({ completed, upcoming_only }) => {
        try {
          let sql = 'SELECT * FROM reminders WHERE user_id = ?';
          const params: any[] = [currentUserId];

          if (completed !== undefined) {
            sql += ' AND is_completed = ?';
            params.push(completed);
          }

          if (upcoming_only) {
            sql += ' AND reminder_date > ?';
            params.push(getCurrentISTTime());
          }

          sql += ' ORDER BY reminder_date ASC';

          const reminders = await query(sql, params);
          return { success: true, reminders, currentTime: getCurrentISTTime() };
        } catch (error) {
          console.error('Get reminders error:', error);
          return { success: false, message: 'Failed to get reminders' };
        }
      },
    }),

    // Check pending reminders
    check_pending_reminders: tool({
      description: 'Check for pending reminders that need user attention',
      parameters: z.object({}),
      execute: async () => {
        try {
          const pendingReminders = await checkPendingReminders(currentUserId);
          return { 
            success: true, 
            pendingCount: pendingReminders.length,
            reminders: pendingReminders 
          };
        } catch (error) {
          console.error('Check reminders error:', error);
          return { success: false, message: 'Failed to check reminders' };
        }
      },
    }),

    // Add other tools similarly...
  };
}

export async function POST(req: NextRequest) {
  try {
    const { user_id, message } = await req.json();

    if (!user_id || !message) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: user_id and message' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Simple message format
    const messages = [
      {
        role: 'user' as const,
        content: message,
      }
    ];

    // Create tools with user ID
    const tools = createToolsWithUserId(user_id);

    // Check pending reminders
    const pendingReminders = await checkPendingReminders(user_id);
    let reminderContext = '';
    
    if (pendingReminders.length > 0) {
      reminderContext = `\nUser के ${pendingReminders.length} pending reminders हैं। कृपया उन्हें बताएं।`;
    }

    // Use streamText for better performance
    const result = await streamText({
      model: google('gemini-2.0-flash-exp'),
      messages: convertToCoreMessages(messages),
      tools,
      system: `You are Nityasha, a helpful personal assistant. ${reminderContext}
      
      ## Available Tools:
      - get_current_weather: Get weather information
      - google_search: Search Google for information
      - add_reminder: Add new reminders
      - get_reminders: Get user's reminders
      - check_pending_reminders: Check pending reminders
      
      Always use tools when user asks for relevant information.
      Respond in Hindi/Hinglish for Indian users.`,
      maxSteps: 5,
      temperature: 0.7,
    });

    return result.toDataStreamResponse();

  } catch (error) {
    console.error('API error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' }}
    );
  }
}
