import { WebSocketServer } from 'ws';
import { connectToDatabase } from '@/lib/db';
import { RowDataPacket, FieldPacket } from 'mysql2/promise';
import { NextApiRequest, NextApiResponse } from 'next';

const wss = new WebSocketServer({ noServer: true });

wss.on('connection', (ws) => {
  ws.on('message', async (message) => {
    try {
      const { userId } = JSON.parse(message.toString());
      if (!userId) {
        ws.send(JSON.stringify({ error: 'User ID is required' }));
        return;
      }
      
      const connection = await connectToDatabase();
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT * FROM chatRequests WHERE userId = ? ORDER BY id DESC LIMIT 1',
        [userId]
      );
      
      await connection.end();
      
      if (rows.length === 0) {
        ws.send(JSON.stringify({ message: 'No chat requests found for this user' }));
      } else {
        ws.send(JSON.stringify(rows[0]));
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      ws.send(JSON.stringify({ error: 'Failed to fetch chat request' }));
    }
  });
});

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (res.socket?.server?.wss) {
    res.end();
    return;
  }

  res.socket.server.wss = wss;
  res.end();
}
