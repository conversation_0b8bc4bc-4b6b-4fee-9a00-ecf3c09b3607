import mysql from 'mysql2/promise';

export default async function handler(req, res) {
  const connection = await mysql.createConnection({
    host: process.env.MYSQL_HOST,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
  });

  try {
    const [rows] = await connection.execute('SELECT * FROM consultants');
    res.status(200).json(rows);
  } catch (error) {
    console.error(error); // Ensure 'error' is used.
    res.status(500).json({ error: 'Database query failed a' });
  } finally {
    connection.end();
  }
}
