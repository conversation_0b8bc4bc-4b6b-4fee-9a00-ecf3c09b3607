import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity, ActivityIndicator, Text } from 'react-native';
import tw from 'twrnc';
import { Check } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';

const FETCH_CONSULTANTS_TASK = 'FETCH_CONSULTANTS_TASK';

// Define custom sound asset
const NOTIFICATION_SOUND = require('../assets/bell.wav');

// Define background task
TaskManager.defineTask(FETCH_CONSULTANTS_TASK, async () => {
    try {
        const storedUserId = await AsyncStorage.getItem('userId');
        if (!storedUserId) throw new Error('User ID not found in storage');
        
        // Fetch consultants only if the task is running
        const response = await fetch(`https://nityasha.vercel.app/api/v1/${storedUserId}`);
        if (!response.ok) throw new Error('Failed to fetch data');
        
        const data = await response.json();
        if (Array.isArray(data) && data.length > 0) {
            await Notifications.scheduleNotificationAsync({
                content: {
                    title: 'New User Available',
                    body: `You have ${data.length} new users waiting to chat.`,
                    sound: NOTIFICATION_SOUND,
                    priority: Notifications.AndroidNotificationPriority.HIGH,
                },
                trigger: null, // Instant notification
            });
        }
    } catch (error) {
        console.error('Error fetching consultants in background:', error);
    }
});

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldVibrate: true, // Enable vibration
  }),
});

// Register background fetch task
const registerBackgroundFetchAsync = async () => {
  try {
    await TaskManager.registerTaskAsync(FETCH_CONSULTANTS_TASK, {
      minimumInterval: 6000000 * 100000000, // 15 minutes
      stopOnTerminate: false,
      startOnBoot: true,
    });
  } catch (err) {
    console.log("Task Register Error:", err);
  }
};

const MessagesPendingH = () => {
    const [consultants, setConsultants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [userId, setUserId] = useState(null);
    const [isApproving, setIsApproving] = useState(false);
    const [approvedConsultants, setApprovedConsultants] = useState(new Set());
    const maxConsultantsToShow = 5;
    const navigation = useNavigation();

    useEffect(() => {
        const requestPermissions = async () => {
            const { status } = await Notifications.getPermissionsAsync();
            if (status !== 'granted') {
                const { status: newStatus } = await Notifications.requestPermissionsAsync();
                if (newStatus !== 'granted') {
                    console.log('Notification permissions not granted');
                }
            }
        };
        requestPermissions();
    }, []);

    useEffect(() => {
        // Request notification permissions
        const requestNotificationPermissions = async () => {
          const { status: existingStatus } = await Notifications.getPermissionsAsync();
          let finalStatus = existingStatus;
          if (existingStatus !== 'granted') {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
          }
          if (finalStatus !== 'granted') {
            alert('Failed to get push token for push notification!');
            return;
          }
        };

        requestNotificationPermissions();

        // Register background fetch
        const setupBackgroundFetch = async () => {
          try {
            const isRegistered = await TaskManager.isTaskRegisteredAsync(FETCH_CONSULTANTS_TASK);
            if (!isRegistered) {
              await registerBackgroundFetchAsync();
            }
          } catch (err) {
            console.error('Task Setup Error:', err);
          }
        };

        setupBackgroundFetch();

        // Listen for notification responses
        const notificationListener = Notifications.addNotificationReceivedListener(notification => {
          console.log('Notification received:', notification);
        });

        const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
          console.log('Notification response:', response);
          // Handle notification tap here
        });

        return () => {
          Notifications.removeNotificationSubscription(notificationListener);
          Notifications.removeNotificationSubscription(responseListener);
        };
    }, []);

    const fetchConsultants = async () => {
        setLoading(true);
        try {
            const storedUserId = await AsyncStorage.getItem('userId');
            if (!storedUserId) {
                throw new Error('User ID not found in storage');
            }
            setUserId(storedUserId);
            const response = await fetch(`https://nityasha.vercel.app/api/v1/${storedUserId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch data');
            }
            const data = await response.json();
            if (Array.isArray(data)) {
                setConsultants(data);
                notifyNewConsultants(data);
            } else {
                throw new Error('Invalid data format');
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const notifyNewConsultants = async (newConsultants) => {
        if (newConsultants.length > 0) {
            await Notifications.scheduleNotificationAsync({
                content: {
                    title: 'New User Available',
                    body: `You have ${newConsultants.length} new users waiting to chat.`,
                    data: { consultants: newConsultants },
                    sound: NOTIFICATION_SOUND,

                },
                trigger: null,
            });
        }
    };

    useEffect(() => {
        fetchConsultants();
        const intervalId = setInterval(fetchConsultants, 10000);
        return () => clearInterval(intervalId);
    }, []);

    useEffect(() => {
        const checkChatExpiration = () => {
            consultants.forEach((consultant) => {
                const currentTime = Date.now();
                const expirationTime = new Date(consultant.requestDetails?.time).getTime();
                if (currentTime > expirationTime && approvedConsultants.has(consultant.userId)) {
                    updateIsChatOn(consultant.userId, 0);
                }
            });
        };
        const expirationCheckInterval = setInterval(checkChatExpiration, 60000);
        return () => clearInterval(expirationCheckInterval);
    }, [consultants, approvedConsultants]);

    const updateIsChatOn = async (consultantId, status) => {
        try {
            const storedUserId = await AsyncStorage.getItem('userId');
            if (!storedUserId) {
                throw new Error('User ID not found in storage');
            }
            const response = await fetch(`https://nityasha.vercel.app/api/v1/consultantss/${storedUserId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ isChatOn: status }),
            });
            if (!response.ok) {
                throw new Error('Failed to update isChatOn');
            }
            console.log(`isChatOn updated to ${status} successfully for consultant ${consultantId}`);
        } catch (err) {
            console.error(`Error updating isChatOn for consultant ${consultantId}:`, err);
            setError(err.message);
        }
    };

    const approveConsultant = async () => {
        setIsApproving(true);
        try {
            const storedUserIds = await AsyncStorage.getItem('userId');
            if (!storedUserIds) {
                throw new Error('User ID not found in storage');
            }
            const response = await fetch(`https://nityasha.vercel.app/api/v1/${storedUserIds}`);
            if (!response.ok) {
                throw new Error('Failed to fetch consultants');
            }
            const consultants = await response.json();
            const consultant = consultants.find(item => item.consultantDetails.id === parseInt(storedUserIds, 10));
            if (!consultant || !consultant.requestDetails || !consultant.requestDetails.chatRequestId) {
                throw new Error('Invalid consultant data');
            }
            const chatRequestId = consultant.requestDetails.chatRequestId;
            const approvalResponse = await fetch(`https://nityasha.vercel.app/api/v1/${storedUserIds}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ chatRequestId, status: 'approved' }),
            });
            if (!approvalResponse.ok) {
                throw new Error('Failed to approve consultant');
            }
            console.log("Consultant approved successfully");
            return true;
        } catch (err) {
            console.error(err);
            setError(err.message);
            return false;
        } finally {
            setIsApproving(false);
        }
    };

    const handlePress = async (consultantId, time, roomN, bel) => {
        const numericTime = parseFloat(time) || 0;
        const approvalSuccess = await approveConsultant(consultantId);
        const storedUserIds = await AsyncStorage.getItem('userId');
        if (approvalSuccess) {
            setApprovedConsultants(prev => new Set(prev).add(consultantId));
            try {
                const storedUserId = await AsyncStorage.getItem('userId');
                const balanceResponse = await fetch(`https://nityasha.vercel.app/api/v1/file/${storedUserId}`);
                if (!balanceResponse.ok) {
                    throw new Error('Failed to fetch current balance');
                }
                const currentBalanceData = await balanceResponse.json();
                const currentBalance = parseFloat(currentBalanceData.balance) || 0;
                const numericBel = parseFloat(bel) || 0;
                const newBalance = currentBalance + numericBel;

                const response = await fetch(`https://nityasha.vercel.app/api/v1/file/${storedUserId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ balance: newBalance }),
                });
                if (!response.ok) {
                    throw new Error('Failed to add consultant bell or update balance');
                }

                const chatOnResponse = await fetch(`https://nityasha.vercel.app/api/v1/consultantss/${storedUserId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ isChatOn: 1 }),
                });
                if (!chatOnResponse.ok) {
                    throw new Error('Failed to update isChatOn');
                }

                console.log("isChatOn updated successfully");
                navigation.navigate('ChatScreen', { roomN, userId: consultantId ,time});
            } catch (error) {
                console.error('Error updating balance:', error);
            }
        }
    };

    if (error) {
        return (
            <View style={tw`flex-1 items-center justify-center`}>
                <Text style={tw`text-red-500`}>{error}</Text>
            </View>
        );
    }

    return (
        <>
            {Array.isArray(consultants) && consultants.length > 0 ? (
                consultants.slice(0, maxConsultantsToShow).map((consultant, index) => ( // Limit the displayed consultants
                    <View key={index} style={tw`flex items-center justify-between w-full flex-row mt-4`}>
                        <View style={tw`flex items-center justify-center gap-2 flex-row`}>
                            <View style={tw`flex items-center justify-center bg-white w-12 h-12 rounded-full overflow-hidden`}>
                                <Image
                                    style={tw`flex w-full h-full`}
                                    source={{ uri: consultant.userDetails.pfp }}
                                />
                            </View>
                            <View style={tw`flex justify-center items-center`}>
                                <Text style={[tw`text-white`, { fontFamily: 'Urbanist_600SemiBold' }]}>
                                    {consultant.userDetails?.name || 'Unknown'}
                                </Text>
                            </View>
                        </View>
                        <View style={tw`flex items-center justify-center flex-row gap-3`}>
                            <TouchableOpacity
                                style={tw`flex p-4 bg-[#1e1e1e] rounded-full`}
                                onPress={() => handlePress(consultant.userId, consultant.requestDetails?.time, consultant.requestDetails?.roomN, consultant.requestDetails?.bel)}
                                disabled={isApproving}
                            >
                                {isApproving ? (
                                    <ActivityIndicator size="small" color="#fff" />
                                ) : (
                                    <Check color="#fff" />
                                )}
                            </TouchableOpacity>
                            <Text style={[tw`text-white text-lg`, { fontFamily: 'Urbanist_600SemiBold' }]}>
                                ₹{consultant.requestDetails?.bel}
                            </Text>
                        </View>
                    </View>
                ))
            ) : (
                <Text style={tw`text-white`}>No consultants available</Text>
            )}
        </>
    );
};

export default MessagesPendingH;
