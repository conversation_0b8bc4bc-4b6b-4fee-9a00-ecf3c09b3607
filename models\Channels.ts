import mongoose, { Schema, Document } from 'mongoose';

// Define interface for Channel document
interface IChannel extends Document {
  _id: string;
  ownerId: string;
  name: string;
  description: string;
  isPaid: boolean;
  price: number;
  createdAt: Date;
  isOwnerConsultant: (consultantId: string) => boolean;
}

const channelSchema = new Schema({
    _id: {
        type: String,
        required: true,
        alias: 'ownerId'
    },
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
        default: ""
    },
    isPaid: {
        type: Boolean,
        default: false
    },
    price: {
        type: Number,
        default: 0
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
}, { _id: false });

// Method to check if owner is the consultant
channelSchema.methods.isOwnerConsultant = function(consultantId: string): boolean {
    return this._id === consultantId;
};

// Check if the model exists before creating it
const Channel = mongoose.models.Channel || mongoose.model<IChannel>('Channel', channelSchema);

export default Channel;
