import { WebSocketServer } from "ws";
import { NextApiRequest, NextApiResponse } from "next";
import { connectToDatabase } from "@/lib/db";

type ExtendedNextApiResponse = NextApiResponse & {
  socket: {
    server: {
      wss?: WebSocketServer;
      on: (event: string, callback: (req: NextApiRequest, socket: any, head: any) => void) => void;
    };
  };
};

export default function handler(req: NextApiRequest, res: ExtendedNextApiResponse) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  if (!res.socket.server.wss) {
    const wss = new WebSocketServer({ noServer: true });
    res.socket.server.wss = wss;

    wss.on("connection", (ws) => {
      console.log("WebSocket client connected");

      ws.on("message", async (message) => {
        try {
          const { action, userId } = JSON.parse(message.toString());
          const connection = await connectToDatabase();

          if (action === "GET_BALANCE") {
            const [rows] = await connection.query("SELECT balance FROM users WHERE id = ?", [userId]);
            if (Array.isArray(rows) && rows.length > 0) {
              ws.send(JSON.stringify({ balance: rows[0].balance }));
            } else {
              ws.send(JSON.stringify({ error: "User not found" }));
            }
          }

          await connection.end();
        } catch (error) {
          console.error("WebSocket Error:", error);
          ws.send(JSON.stringify({ error: "Server error" }));
        }
      });

      ws.on("close", () => console.log("Client disconnected"));
    });

    res.socket.server.on("upgrade", (req, socket, head) => {
      wss.handleUpgrade(req, socket, head, (ws) => {
        wss.emit("connection", ws, req);
      });
    });
  } else {
    console.log("WebSocket server already running.");
  }

  res.status(200).end();
}
