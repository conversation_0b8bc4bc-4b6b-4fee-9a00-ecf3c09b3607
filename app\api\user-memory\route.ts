import { NextResponse } from 'next/server';
import { UserMemoryManager } from '@/lib/user-memory';

export async function POST(request: Request) {
  try {
    const { userId, key, value, description } = await request.json();

    if (!userId || !key || !value) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const memory = await UserMemoryManager.setMemory({
      userId,
      key,
      value,
      description,
    });

    return NextResponse.json(memory);
  } catch (error) {
    console.error('Error storing memory:', error);
    return NextResponse.json(
      { error: 'Failed to store memory' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const key = searchParams.get('key');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const userIdNum = parseInt(userId);

    if (key) {
      const memory = await UserMemoryManager.getMemory(userIdNum, key);
      if (!memory) {
        return NextResponse.json(
          { error: 'Memory not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(memory);
    }

    const memories = await UserMemoryManager.getAllMemories(userIdNum);
    return NextResponse.json(memories);
  } catch (error) {
    console.error('Error retrieving memories:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve memories' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const key = searchParams.get('key');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const userIdNum = parseInt(userId);

    if (key) {
      await UserMemoryManager.deleteMemory(userIdNum, key);
      return NextResponse.json({ message: 'Memory deleted successfully' });
    }

    await UserMemoryManager.deleteAllMemories(userIdNum);
    return NextResponse.json({ message: 'All memories deleted successfully' });
  } catch (error) {
    console.error('Error deleting memories:', error);
    return NextResponse.json(
      { error: 'Failed to delete memories' },
      { status: 500 }
    );
  }
}
