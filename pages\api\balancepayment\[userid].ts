import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, ResultSetHeader, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    if (req.method === 'GET') {
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        `SELECT balance FROM users WHERE id = ?`,
        [userid]
      );

      if (rows.length === 0) {
        return res.status(404).json({ error: 'User not found' });
      }

      return res.status(200).json({ balance: rows[0].balance });
    }

    if (req.method === 'PUT') {
      const { balance } = req.body;

      if (balance === undefined) {
        return res.status(400).json({ error: 'Balance is required' });
      }

      const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
        `UPDATE users SET balance = ? WHERE id = ?`,
        [balance, userid]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ error: 'User not found or balance not updated' });
      }

      return res.status(200).json({ message: 'Balance updated successfully' });
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
