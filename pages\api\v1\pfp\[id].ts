import { IncomingForm, File } from 'formidable';
import fs from 'fs';
import path from 'path';
import type { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db'; // Ensure this path is correct
import { FieldPacket, ResultSetHeader } from 'mysql2/promise';

export const config = {
    api: {
        bodyParser: false, // Disable the default body parser
    },
};

const uploadDir = path.join(process.cwd(), 'public', 'uploads'); // Define the upload directory

// Ensure the upload directory exists
fs.mkdirSync(uploadDir, { recursive: true });

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
    const { query: { id } } = req; // Get the user ID from the URL

    const form = new IncomingForm();
    
    form.on('fileBegin', (name: string, file: File) => {
        // Set the upload directory, but use 'originalFilename' instead of 'name'
        file.filepath = path.join(uploadDir, file.originalFilename || 'uploadedFile');
    });

    form.parse(req, async (err, fields, files) => {
        if (err) {
            return res.status(500).json({ error: 'Failed to upload file' });
        }

        const file = files.file ? files.file[0] : null; // Extract the first file object
        if (!file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const oldPath = file.filepath; // Temporary file path
        const newFileName = file.originalFilename || 'uploadedFile';
        const newPath = path.join(uploadDir, newFileName);

        // Move the file to the desired location
        fs.rename(oldPath, newPath, async (err) => {
            if (err) {
                return res.status(500).json({ error: 'Failed to save file' });
            }

            // Construct the URL of the uploaded file
            const fileUrl = `http://${req.headers.host}/uploads/${newFileName}`;

            try {
                const connection = await connectToDatabase();

                // SQL Query to update the user in the users table
                const sql = 'UPDATE users SET pfp = ? WHERE id = ?';
                const values = [fileUrl, id]; // Use the user ID from the URL

                const [result]: [ResultSetHeader, FieldPacket[]] = await connection.execute(sql, values);

                if (result.affectedRows === 0) {
                    return res.status(404).json({ error: 'User not found' });
                }

                res.status(200).json({ message: 'File uploaded successfully', filename: newFileName, fileUrl });
            } catch (dbError: unknown) {
                if (dbError instanceof Error) {
                    console.error('Database update failed:', dbError.message);
                    res.status(500).json({ error: 'Failed to update database', details: dbError.message });
                } else {
                    console.error('Database update failed:', dbError);
                    res.status(500).json({ error: 'Failed to update database', details: 'An unknown error occurred.' });
                }
            }
        });
    });
};

export default handler;
