generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model UserMemory {
  id          Int      @id @default(autoincrement())
  userId      Int
  key         String
  value       String   @db.Text
  description String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@unique([userId, key])
}

model Calls {
  id                               Int      @id @default(autoincrement())
  consantent_id                    Int
  customer_id                      Int
  room_id                          String   @db.Text
  Status                           Int
  timer                            String   @db.VarChar(11)
  type                             Int
  created_at                       DateTime @default(now()) @db.Timestamp(0)
  updated_at                       DateTime @default(now()) @db.Timestamp(0)
  users_Calls_consantent_idTousers users    @relation("Calls_consantent_idTousers", fields: [consantent_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "Calls_ibfk_1")
  users_Calls_customer_idTousers   users    @relation("Calls_customer_idTousers", fields: [customer_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "Calls_ibfk_2")

  @@index([consantent_id], map: "consantent_id")
  @@index([customer_id], map: "customer_id")
}

model admins {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  uid        String    @unique(map: "admins_uid_unique") @db.VarChar(20)
  username   String
  email      String    @unique(map: "admins_email_unique")
  password   String
  code       String    @db.Text
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
}

model analytics {
  id        Int      @id @default(autoincrement())
  user_id   Int?
  action    String   @db.VarChar(255)
  page      String?  @db.VarChar(255)
  timestamp DateTime @default(now()) @db.Timestamp(0)
  details   String?  @db.LongText
  users     users?   @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "analytics_ibfk_1")

  @@index([user_id], map: "user_id")
}

model astrologerReview {
  review_id     Int         @id @default(autoincrement())
  consultantId  Int
  reviewer_name String      @db.VarChar(255)
  rating        Int?
  review_text   String?     @db.Text
  review_date   DateTime    @default(now()) @db.Timestamp(0)
  user_id       Int?
  consultants   consultants @relation(fields: [consultantId], references: [id], onUpdate: Restrict, map: "astrologerReview_ibfk_1")
  users         users?      @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "fk_user_id")

  @@index([consultantId], map: "consultantId")
  @@index([user_id], map: "fk_user_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model card {
  benknum Int
  ifc     Int
  name    Int

  @@ignore
}

model categories {
  id                                             Int             @id @default(autoincrement())
  name                                           String          @unique(map: "name") @db.VarChar(255)
  icon                                           String          @db.Text
  description                                    String?         @db.Text
  parent_id                                      Int?
  created_at                                     DateTime        @default(now()) @db.Timestamp(0)
  updated_at                                     DateTime        @default(now()) @db.Timestamp(0)
  slug                                           String          @db.Text
  categories                                     categories?     @relation("categoriesTocategories", fields: [parent_id], references: [id], onUpdate: Restrict, map: "categories_ibfk_1")
  other_categories                               categories[]    @relation("categoriesTocategories")
  consultants_consultants_categoriesTocategories consultants[]   @relation("consultants_categoriesTocategories")
  subcategories                                  subcategories[]

  @@index([parent_id], map: "parent_id")
}

model chatRequests {
  id           Int                  @id @default(autoincrement())
  userId       Int
  consultantId Int
  status       chatRequests_status? @default(pending)
  bel          String               @db.Text
  time         String               @db.Text
  RoomN        String?              @db.VarChar(255)
  created_at   DateTime             @default(now()) @db.Timestamp(0)
  consultants  consultants          @relation(fields: [consultantId], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "chatRequests_ibfk_1")
  users        users                @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "chatRequests_ibfk_2")

  @@index([consultantId], map: "consultantId")
  @@index([userId], map: "userId")
}

model chat_sessions {
  id            Int      @id @default(autoincrement())
  user_id       Int
  consultant_id Int
  total_cost    Decimal  @db.Decimal(10, 2)
  chat_duration Int
  created_at    DateTime @default(now()) @db.Timestamp(0)
}

model compony_money {
  id    Int @id @default(autoincrement())
  money Int
  added Int
}

model consultants {
  id                                            Int                @id @default(autoincrement())
  name                                          String?            @db.VarChar(100)
  per_minute_rate                               Decimal?           @db.Decimal(10, 2)
  birth_date                                    DateTime           @db.Date
  total_sales                                   String             @db.VarChar(255)
  isLive                                        Boolean
  password                                      String             @db.Text
  pfp                                           String             @db.Text
  TOTAL_REQUEST                                 Int
  GENDER                                        Int
  profile_url                                   Int
  categories                                    Int
  auth_check                                    Boolean
  code                                          Int
  number                                        String             @db.Text
  email                                         String             @db.Text
  balance                                       String             @db.Text
  verified_email                                Boolean
  categoriess                                   String             @db.Text
  exp                                           Int
  isChatOn                                      Boolean
  thumnel                                       String             @db.Text
  active                                        Boolean
  push_token                                    String?            @db.VarChar(255)
  Review                                        Review[]
  Widowable                                     Widowable[]
  astrologerReview                              astrologerReview[]
  chatRequests                                  chatRequests[]
  categories_consultants_categoriesTocategories categories         @relation("consultants_categoriesTocategories", fields: [categories], references: [id], onUpdate: Restrict, map: "consultants_ibfk_1")
  followers                                     followers[]

  @@index([categories], map: "categories")
}

model contacts {
  id         Int      @id @default(autoincrement())
  name       String   @db.VarChar(255)
  email      String   @db.VarChar(255)
  subject    String   @db.VarChar(255)
  message    String   @db.Text
  created_at DateTime @default(now()) @db.Timestamp(0)
}

model earnings {
  id           Int                   @id @default(autoincrement())
  user_id      Int
  amount       Decimal               @db.Decimal(10, 2)
  earning_type earnings_earning_type
  description  String?               @db.Text
  date_earned  DateTime              @db.Date
  created_at   DateTime              @default(now()) @db.Timestamp(0)
  updated_at   DateTime              @default(now()) @db.Timestamp(0)
  users        users                 @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "earnings_ibfk_1")

  @@index([user_id], map: "user_id")
}

model gifts {
  id            Int    @id @default(autoincrement())
  name          String @db.VarChar(255)
  icon          String @db.VarChar(255)
  amount        Int
  status        Int
  user_id       Int
  consantent_id String @db.Text
}

model ips {
  id Int @id @default(autoincrement())
  ip Int
}

model live {
  id               Int      @id @default(autoincrement())
  user_id          Int
  title            String   @db.Text
  room_name        String   @db.VarChar(255)
  agora_channel_id String   @db.VarChar(255)
  thumbnail        String   @db.VarChar(255)
  created_at       DateTime @default(now()) @db.Timestamp(0)
  users            users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "live_ibfk_1")

  @@index([user_id], map: "user_id")
}

model messages {
  id          Int              @id @default(autoincrement())
  sender_id   Int
  receiver_id Int
  content     String           @db.Text
  timestamp   DateTime         @default(now()) @db.Timestamp(0)
  status      messages_status? @default(sent)
}

model notifications {
  id                Int                             @id @default(autoincrement())
  notification_type notifications_notification_type
  notification_text String                          @db.Text
  created_at        DateTime                        @default(now()) @db.Timestamp(0)
  updated_at        DateTime                        @default(now()) @db.Timestamp(0)
}

model orders {
  id         Int           @id @default(autoincrement())
  Consantent String        @db.VarChar(255)
  Customer   String        @db.VarChar(255)
  Status     orders_Status
  Price      Decimal       @db.Decimal(10, 2)
  Total_Time Int
  userid     Int
  Created_at DateTime      @default(now()) @db.Timestamp(0)
}

model pages {
  id         Int           @id @default(autoincrement())
  title      String        @unique(map: "title") @db.VarChar(255)
  content    String        @db.Text
  status     pages_status? @default(draft)
  slug       String        @unique(map: "slug") @db.VarChar(255)
  created_at DateTime      @default(now()) @db.Timestamp(0)
  updated_at DateTime      @default(now()) @db.Timestamp(0)
}

model partners {
  id           Int     @id @default(autoincrement())
  name         String? @db.VarChar(50)
  fullName     String? @db.VarChar(100)
  email        String? @unique(map: "email") @db.VarChar(100)
  password     String? @db.VarChar(100)
  mobileNumber String? @db.VarChar(15)
  Category     String  @db.Text
}

model payments {
  id   Int     @id @default(autoincrement())
  name String  @db.Text
  icon String  @db.Text
  api  String  @db.Text
  sec  String  @db.Text
  more String  @db.Text
  isOn Boolean
}

model post_images {
  id         Int      @id @default(autoincrement())
  post_id    Int
  image_url  String   @db.VarChar(255)
  created_at DateTime @default(now()) @db.Timestamp(0)
  posts      posts    @relation(fields: [post_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "post_images_ibfk_1")

  @@index([post_id], map: "post_id")
}

model posts {
  id             Int           @id @default(autoincrement())
  title          String        @db.VarChar(255)
  content        String        @db.Text
  post_thumbnail String        @db.Text
  author         String        @db.VarChar(255)
  status         posts_status
  created_at     DateTime      @default(now()) @db.Timestamp(0)
  updated_at     DateTime      @default(now()) @db.Timestamp(0)
  slug           String?       @unique(map: "slug") @db.VarChar(255)
  category       String?       @db.VarChar(255)
  post_images    post_images[]
}

model profiles {
  id          Int      @id @default(autoincrement())
  username    String   @db.VarChar(255)
  address     String   @db.VarChar(255)
  phoneNumber String   @db.VarChar(15)
  category    String   @db.VarChar(100)
  createdAt   DateTime @default(now()) @db.Timestamp(0)
  updatedAt   DateTime @default(now()) @db.Timestamp(0)
}

model reports {
  id                               Int                 @id @default(autoincrement())
  reporter_id                      Int
  reported_id                      Int
  report_type                      reports_report_type
  report_reason                    String              @db.Text
  STATUS                           Boolean
  created_at                       DateTime            @default(now()) @db.Timestamp(0)
  updated_at                       DateTime            @default(now()) @db.Timestamp(0)
  users_reports_reporter_idTousers users               @relation("reports_reporter_idTousers", fields: [reporter_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "reports_ibfk_1")
  users_reports_reported_idTousers users               @relation("reports_reported_idTousers", fields: [reported_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "reports_ibfk_2")

  @@index([reported_id], map: "reported_id")
  @@index([reporter_id], map: "reporter_id")
}

model sessions {
  id         Int    @id @default(autoincrement())
  session_id String @unique(map: "session_id") @db.VarChar(255)
}

model settings {
  id          Int    @id @default(autoincrement())
  title       String @db.Text
  favicon     String @db.Text
  description String @db.Text
  logo        String @db.Text
}

model silder {
  id  Int    @id @default(autoincrement())
  url String @db.Text
}

model subcategories {
  id          Int        @id @default(autoincrement())
  category_id Int
  name        String     @db.VarChar(255)
  description String?    @db.Text
  created_at  DateTime   @default(now()) @db.Timestamp(0)
  categories  categories @relation(fields: [category_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "subcategories_ibfk_1")

  @@index([category_id], map: "category_id")
}

model team {
  id         Int      @id @default(autoincrement())
  name       String   @db.VarChar(255)
  role       String   @db.VarChar(100)
  email      String   @unique(map: "email") @db.VarChar(255)
  password   String   @db.Text
  bio        String?  @db.Text
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(now()) @db.Timestamp(0)
}

model tickets {
  id                 Int               @id @default(autoincrement())
  user_id            Int
  ticket_title       String            @db.VarChar(255)
  ticket_description String            @db.Text
  status             tickets_status?   @default(open)
  priority           tickets_priority? @default(medium)
  created_at         DateTime          @default(now()) @db.Timestamp(0)
  updated_at         DateTime          @default(now()) @db.Timestamp(0)
  users              users             @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "tickets_ibfk_1")

  @@index([user_id], map: "user_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model traffic_data_ordered {
  date    DateTime? @db.Date
  desktop Int?
  mobile  Int?

  @@ignore
}

model traffic_data_ordered_selles {
  id         Int      @id @default(autoincrement())
  browser    String   @db.VarChar(50)
  date       DateTime @db.Date
  visitors   Int      @default(0)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(now()) @db.Timestamp(0)
}

model user_status {
  user_id      Int                @id
  status       user_status_status
  last_updated DateTime           @default(now()) @db.Timestamp(0)
}

model users {
  GENDER                             Int?
  TOTAL_REQUEST                      Int?
  STATUS                             Boolean?           @default(false)
  id                                 Int                @id @default(autoincrement())
  profile_url                        String             @db.VarChar(255)
  contact_no                         String             @db.Text
  email                              String             @unique(map: "email") @db.VarChar(255)
  password                           String             @db.VarChar(255)
  birth_date                         String             @db.Text
  birth_time                         String             @db.Text
  total_sales                        String?            @default("0.00") @db.Text
  created_at                         DateTime           @default(now()) @db.Timestamp(0)
  balance                            String?            @default("10.00") @db.Text
  isLive                             Boolean
  remember_token                     String?            @db.VarChar(100)
  pfp                                String             @default("https://upload.wikimedia.org/wikipedia/commons/thumb/2/2c/Default_pfp.svg/1024px-Default_pfp.svg.png") @db.Text
  role                               String             @default("user") @db.VarChar(50)
  username                           String             @db.Text
  handle                             String             @db.Text
  pushtoken                          String             @db.Text
  Calls_Calls_consantent_idTousers   Calls[]            @relation("Calls_consantent_idTousers")
  Calls_Calls_customer_idTousers     Calls[]            @relation("Calls_customer_idTousers")
  Review                             Review[]
  analytics                          analytics[]
  astrologerReview                   astrologerReview[]
  chatRequests                       chatRequests[]
  earnings                           earnings[]
  followers                          followers[]
  live                               live[]
  reports_reports_reporter_idTousers reports[]          @relation("reports_reporter_idTousers")
  reports_reports_reported_idTousers reports[]          @relation("reports_reported_idTousers")
  tickets                            tickets[]
}

model Traffic {
  id      Int       @id @default(autoincrement())
  desktop Boolean
  mobile  Boolean
  date    DateTime? @default(now()) @db.DateTime(0)
  ip      String    @db.VarChar(255)
  country String    @db.VarChar(255)
  city    String    @db.VarChar(255)
}

model Review {
  review_id     Int         @id @default(autoincrement())
  consultantId  Int
  reviewer_name String      @db.VarChar(255)
  rating        Int
  review_text   String?     @db.Text
  review_date   DateTime    @default(now()) @db.Timestamp(0)
  user_id       Int?
  consultants   consultants @relation(fields: [consultantId], references: [id], onDelete: Cascade, map: "fk_review_consultant_id")
  users         users?      @relation(fields: [user_id], references: [id], map: "fk_review_user_id")

  @@index([consultantId], map: "fk_review_consultant_id")
  @@index([user_id], map: "fk_review_user_id")
}

model Widowable {
  id          Int               @id @default(autoincrement())
  user_id     Int
  money       Int
  description String?           @db.Text
  status      Widowable_status? @default(active)
  widow_date  DateTime?         @db.DateTime(0)
  created_at  DateTime?         @default(now()) @db.DateTime(0)
  updated_at  DateTime?         @default(now()) @db.DateTime(0)
  consultants consultants       @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "Widowable_ibfk_1")

  @@index([user_id], map: "user_id")
}

model followers {
  id          Int         @id @default(autoincrement())
  follower_id Int
  followed_id Int
  created_at  DateTime    @default(now()) @db.Timestamp(0)
  users       users       @relation(fields: [follower_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "followers_ibfk_1")
  consultants consultants @relation(fields: [followed_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "followers_ibfk_2")

  @@unique([follower_id, followed_id], map: "follower_id")
  @@index([followed_id], map: "followed_id")
}

model visits {
  id         Int       @id @default(autoincrement())
  visit_time DateTime? @db.DateTime(0)
  message    String?   @db.VarChar(255)
  ip_address String?   @db.VarChar(45)
  latitude   Float?
  longitude  Float?
}

enum notifications_notification_type {
  message
  alert
  reminder
}

enum user_status_status {
  online
  offline
}

enum orders_Status {
  Pending
  Completed
  Cancelled
}

enum earnings_earning_type {
  commission
  bonus
  salary
  other
}

enum chatRequests_status {
  pending
  approved
  declined
}

enum reports_report_type {
  message
  user
}

enum pages_status {
  draft
  published
  archived
}

enum tickets_status {
  open
  in_progress
  resolved
  closed
}

enum posts_status {
  Draft
  Published
  Archived
}

enum tickets_priority {
  low
  medium
  high
}

enum messages_status {
  sent
  delivered
  read
}

enum Widowable_status {
  active
  widowed
}
