import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';
import { OkPacket } from 'mysql2'; // Adjust this import based on your actual MySQL library

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { reporter_id, reported_id, report_type, report_reason } = req.body;

        // Validate input
        if (!reporter_id || !reported_id || !report_type || !report_reason) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        try {
            // Insert the report into the database
            const result = await query(
                'INSERT INTO reports (reporter_id, reported_id, report_type, report_reason, STATUS, created_at) VALUES (?, ?, ?, ?, 0, NOW())',
                [reporter_id, reported_id, report_type, report_reason]
            );

            // Check the type of result and cast it
            const insertResult = result as OkPacket; // Assert the result type

            // Access insertId
            const reportId = insertResult.insertId || null; // Safely access insertId

            return res.status(201).json({
                message: 'Report successfully created',
                reportId: reportId
            });
        } catch (error) {
            console.error('Error creating report:', error);
            return res.status(500).json({ message: 'Server error' });
        }
    } else {
        // Handle other HTTP methods
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
