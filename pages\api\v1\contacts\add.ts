import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '../../../../lib/db';
import { RowDataPacket } from 'mysql2/promise';

interface ContactRequest {
  contact_name: string;
  phone_number: string;
  user_id: number;
  email?: string;
}

interface ContactRow extends RowDataPacket {
  id: number;
  contact_name: string;
  phone_number: string;
  user_id: number;
  email?: string;
  is_app_user: boolean;
  app_user_id?: number;
  created_at: Date;
  updated_at: Date;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { contact_name, phone_number, user_id, email }: ContactRequest = req.body;

    // Validate required fields
    if (!contact_name || !phone_number || !user_id) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    const db = await connectToDatabase();

    // Clean phone number
    const cleanNumber = phone_number
      .replace(/[\s-()]/g, '')
      .replace(/^\+?91/, '')
      .replace(/^0+/, '');

    // Check if contact already exists
    const [existingContacts] = await db.execute<ContactRow[]>(
      'SELECT id FROM contacts_list WHERE user_id = ? AND phone_number = ?',
      [user_id, cleanNumber]
    );

    if (existingContacts.length > 0) {
      return res.status(409).json({ message: 'Contact already exists' });
    }

    // Check if this number belongs to an app user
    const [appUsers] = await db.execute<ContactRow[]>(
      'SELECT id FROM users WHERE email = ?',
      [cleanNumber]
    );

    const isAppUser = appUsers.length > 0;
    const appUserId = isAppUser ? appUsers[0].id : null;

    // OPTIONAL: Debug log
    console.log('Inserting contact:', {
      contact_name,
      phone_number: cleanNumber,
      user_id,
      email: email ?? null,
      isAppUser,
      appUserId: appUserId ?? null
    });

    // Add contact to database
    const [result] = await db.execute(
      `INSERT INTO contacts_list 
       (contact_name, phone_number, user_id, email, is_app_user, app_user_id) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        contact_name,
        cleanNumber,
        user_id,
        email ?? null,
        isAppUser,
        appUserId ?? null
      ]
    );

    // Get the newly created contact
    const [newContacts] = await db.execute<ContactRow[]>(
      'SELECT * FROM contacts_list WHERE id = LAST_INSERT_ID()'
    );

    return res.status(201).json(newContacts[0]);

  } catch (error: any) {
    console.error('Contact creation error:', error);

    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({
        message: 'This contact already exists for this user'
      });
    }

    return res.status(500).json({
      message: 'Failed to create contact'
    });
  }
}
