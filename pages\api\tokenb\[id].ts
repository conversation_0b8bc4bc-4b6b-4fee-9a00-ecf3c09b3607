import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { ResultSetHeader } from 'mysql2/promise'; // Ensure you have MySQL2 typings installed

type ResponseData = {
  message?: string;
  error?: string;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse<ResponseData>) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { id } = req.query;
  const { pushToken } = req.body;

  if (!id || Array.isArray(id) || Number.isNaN(Number(id))) {
    return res.status(400).json({ error: 'Invalid or missing consultant ID' });
  }

  if (!pushToken || typeof pushToken !== 'string' || pushToken.trim() === '') {
    return res.status(400).json({ error: 'Missing or invalid push token' });
  }

  let connection;
  try {
    // Establish database connection
    connection = await connectToDatabase();
    if (!connection) {
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Execute query
    const [result] = await connection.execute<ResultSetHeader>(
      'UPDATE consultants SET push_token = ? WHERE id = ?',
      [pushToken, id]
    );

    if (result.affectedRows > 0) {
      return res.status(200).json({ message: 'Push token updated successfully' });
    } else {
      return res.status(404).json({ error: 'Consultant not found' });
    }
  } catch (error: any) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Database error: ' + error.message });
  } finally {
    // Ensure database connection is closed
    if (connection) {
      await connection.end().catch((err) => console.error('Error closing connection:', err));
    }
  }
}
