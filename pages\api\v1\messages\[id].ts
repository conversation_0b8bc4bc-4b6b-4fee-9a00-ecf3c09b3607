import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const {
    query: { id }, // Extract the id from the URL
    method,
  } = req;

  // Check if the request method is GET
  if (method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  let connection;
  try {
    // Create a database connection
    connection = await connectToDatabase();
    
    // Execute the query to fetch messages for the specific user
    const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
      'SELECT * FROM messages WHERE recipient_id = ? OR sender_id = ?',
      [id, id] // Use the id from the URL
    );

    // Check if no messages are found
    if (rows.length === 0) {
      return res.status(404).json({ message: 'No messages found for this user' });
    }

    // Send the result back as JSON
    res.status(200).json(rows);
  } catch (error) {
    // Log the error and send a 500 response
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  } finally {
    // Ensure the connection is closed
    if (connection) {
      await connection.end();
    }
  }
}
  