import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '../../../../lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';
import redis from '../../../../lib/redis';
import Channel from '@/models/Channels';
import mongoose from 'mongoose';

interface Consultant extends RowDataPacket {
  id: number;
  name: string;
  email: string;
  phone: string;
  categories: number;
  category_name: string;
  small: number; // 0 or 1, renamed from smaill
}

const CACHE_KEY = 'consultants:all';
const CACHE_TTL = 300; // 5 minutes

async function ensureMongoConnected() {
  if (mongoose.connection.readyState !== 1) {
    await mongoose.connect(process.env.MONGODB_URI as string);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    let connection;
    try {
<<<<<<< HEAD
      // Connect to MongoDB to get all channels
      if (mongoose.connection.readyState !== 1) {
        await mongoose.connect(process.env.MONGODB_URI as string);
      }

      // Get all channels
      const channels = await Channel.find({});

      // Create a map of channel owner IDs for quick lookup
=======
      await ensureMongoConnected();

      // Get all channel owners from MongoDB
      const channels = await Channel.find({});
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989
      const channelOwnerIds = new Map();
      channels.forEach(channel => {
        channelOwnerIds.set(channel._id.toString(), true);
      });

<<<<<<< HEAD
      // Check if Redis is connected (not using MockRedis)
      let useRedisCache = true;

      // If redis is an instance of MockRedis, we'll skip the cache
      if ((redis as any).cache instanceof Map) {
        console.log('Redis is not connected, skipping cache and fetching directly from MySQL');
        useRedisCache = false;
      }

      // Try to get data from cache if Redis is connected
      if (useRedisCache) {
        const cachedData = await redis.get(CACHE_KEY);
        if (cachedData) {
          const consultants = JSON.parse(cachedData);

          // Add isChannelOwner field to each consultant
          consultants.forEach((consultant: Consultant) => {
            consultant.isChannelOwner = channelOwnerIds.has(consultant.id.toString());
          });

          return res.status(200).json(consultants);
        }
      }

      // If no cached data or Redis is not connected, query the database
      const connection = await connectToDatabase();

      const query = `
        SELECT consultants.*,
               CONCAT_WS('/ ', categories.name,
                 GROUP_CONCAT(DISTINCT subcategories.name ORDER BY subcategories.name SEPARATOR ',')
=======
      // Check Redis cache
      const cachedData = await redis.get(CACHE_KEY);
      if (cachedData) {
        const consultants: Consultant[] = JSON.parse(cachedData);
        return res.status(200).json(consultants);
      }

      // Connect to MySQL
      connection = await connectToDatabase();

      await connection.query('SET SESSION group_concat_max_len = 100000;');

      const query = `
        SELECT consultants.*, 
               CONCAT_WS('/ ', categories.name, 
                 GROUP_CONCAT(DISTINCT subcategories.name ORDER BY subcategories.name SEPARATOR ', ')
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989
               ) AS category_name
        FROM consultants
        LEFT JOIN categories ON consultants.categories = categories.id
        LEFT JOIN consultant_subcategory ON consultants.id = consultant_subcategory.consultant_id
        LEFT JOIN subcategories ON consultant_subcategory.subcategory_id = subcategories.id
        GROUP BY consultants.id, categories.name;
      `;

      const [rows]: [Consultant[], FieldPacket[]] = await connection.query(query);

      if (!rows.length) {
        return res.status(404).json({ message: 'No consultants found' });
      }

      const formattedRows = rows.map(consultant => ({
        small: channelOwnerIds.has(consultant.id.toString()) ? 1 : 0,
        ...consultant
      }));

<<<<<<< HEAD
      // Store the basic results in Redis cache only if Redis is connected
      if (useRedisCache) {
        try {
          await redis.setex(CACHE_KEY, CACHE_TTL, JSON.stringify(rows));
        } catch (redisError) {
          console.error('Failed to store data in Redis:', redisError);
          // Continue execution even if Redis storage fails
        }
      }
=======
      await redis.set(CACHE_KEY, JSON.stringify(formattedRows), 'EX', CACHE_TTL);
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989

      res.status(200).json(formattedRows);
    } catch (error) {
      console.error('Query failed:', error);
      res.status(500).json({ error: 'Failed to fetch consultants' });
    } finally {
      if (connection) await connection.end();
    }
<<<<<<< HEAD
  }
  // Handle POST request for checking channel ownership
=======
  } 
  
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989
  else if (req.method === 'POST' && req.body.action === 'checkOwner') {
    const { channelId, consultantId } = req.body;

    if (!channelId || !consultantId) {
      return res.status(400).json({ error: 'Missing required parameters: channelId and consultantId' });
    }

    try {
      await ensureMongoConnected();

      const channel = await Channel.findById(channelId);

      if (!channel) {
        return res.status(404).json({ error: 'Channel not found' });
      }

      const isOwner = channel._id.toString() === consultantId.toString();

<<<<<<< HEAD
      return res.status(200).json({
        isOwner,
=======
      return res.status(200).json({ 
        small: isOwner ? 1 : 0,
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989
        channelId,
        consultantId
      });
    } catch (error) {
      console.error('Error checking channel ownership:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
<<<<<<< HEAD
  }
=======
  } 
  
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989
  else {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }
}
