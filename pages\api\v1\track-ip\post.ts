import { connectToDatabase } from '@/lib/db';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method Not Allowed' });
    }

    const { userId, ip } = req.body;

    if (!userId || !ip) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    try {
        const db = await connectToDatabase();
        const [result] = await db.execute(
            'INSERT INTO track_ips (userId, ip) VALUES (?, ?)',
            [userId, ip]
        );
        db.end();
        
        res.status(201).json({ message: 'User saved', id: result.insertId });
    } catch (error) {
        console.error('Database error:', error);
        res.status(500).json({ message: 'Database error', error });
    }
}
