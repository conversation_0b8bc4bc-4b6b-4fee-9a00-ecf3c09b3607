import { NextApiRequest, NextApiResponse } from 'next';
import mongoose from 'mongoose';
import Channel from '@/models/Channels';

async function connectDB() {
  if (mongoose.connection.readyState !== 1) {
    await mongoose.connect(process.env.MONGODB_URI as string, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    await connectDB();

    const { id } = req.query;
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid ID' });
    }

    const ownsChannel = await Channel.exists({ _id: id });

    return res.status(200).json({ ownsChannel: ownsChannel ? 1 : 0 });
  } catch (error) {
    console.error('Error checking channel ownership:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
