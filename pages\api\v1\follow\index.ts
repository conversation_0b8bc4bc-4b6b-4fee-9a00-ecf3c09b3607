import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;

  switch (method) {
    case 'POST': {
      // Add a new follower
      const { follower_id, followed_id } = req.body;

      if (!follower_id || !followed_id) {
        return res.status(400).json({ message: 'follower_id and followed_id are required' });
      }

      try {
        const newFollower = await prisma.followers.create({
          data: {
            follower_id,
            followed_id,
          },
        });
        return res.status(201).json(newFollower);
      } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Unable to add follower', error });
      }
    }

    case 'DELETE': {
      // Remove a follower
      const { follower_id, followed_id } = req.body;

      if (!follower_id || !followed_id) {
        return res.status(400).json({ message: 'follower_id and followed_id are required' });
      }

      try {
        await prisma.followers.delete({
          where: {
            follower_id_followed_id: {
              follower_id,
              followed_id,
            },
          },
        });
        return res.status(200).json({ message: 'Follower removed successfully' });
      } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Unable to remove follower', error });
      }
    }

    case 'GET': {
      // Get a list of followers or following
      const { userId, type } = req.query;

      if (!userId || !type || (type !== 'followers' && type !== 'following')) {
        return res.status(400).json({ message: 'userId and valid type (followers/following) are required' });
      }

      try {
        let result;
        if (type === 'followers') {
          result = await prisma.followers.findMany({
            where: { followed_id: parseInt(userId as string, 10) },
            include: { follower: true },
          });
        } else {
          result = await prisma.followers.findMany({
            where: { follower_id: parseInt(userId as string, 10) },
            include: { followed: true },
          });
        }
        return res.status(200).json(result);
      } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Unable to fetch followers/following', error });
      }
    }

    default:
      res.setHeader('Allow', ['POST', 'DELETE', 'GET']);
      res.status(405).end(`Method ${method} Not Allowed`);
  }
}
