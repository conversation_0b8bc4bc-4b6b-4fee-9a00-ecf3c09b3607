{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "pages/api/v1/live/[id]", "pages/api/v1/live/stop", "pages/api/v1/live/post", "pages/api/v1/search/search", "pages/api/v1/upload/upload", "pages/api/partner/notification/import React, { useEffect, useState } f", "pages/api/partner/notification/index"], "exclude": ["node_modules"]}