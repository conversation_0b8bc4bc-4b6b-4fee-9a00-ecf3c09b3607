import mysql from 'mysql2/promise';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { sessionId } = req.query;

  const connection = await mysql.createConnection({
    host: process.env.MYSQL_HOST,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
  });

  try {
    const [rows] = await connection.execute(
      'SELECT * FROM chat_sessions WHERE id = ?',
      [sessionId]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Chat session not found' });
    }

    res.status(200).json(rows[0]);
  } catch (error) {
    console.error(error); // Log the error to avoid the unused variable issue
    res.status(500).json({ error: 'Database query failed a' });
  } finally {
    connection.end();
  }
}
