import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '../../../../lib/db';
import { RowDataPacket } from 'mysql2/promise';

interface ContactRow extends RowDataPacket {
  id: number;
  contact_name: string;
  phone_number: string;
  user_id: number;
  email?: string;
  is_app_user: boolean;
  app_user_id?: number;
  created_at: Date;
  updated_at: Date;
  app_user_name?: string;
  app_user_email?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const db = await connectToDatabase();

    const [contacts] = await db.execute<ContactRow[]>(
      `SELECT 
        c.*,
        u.username as app_user_name,
        u.email as app_user_email
       FROM contacts_list c
       LEFT JOIN users u ON c.app_user_id = u.id
       WHERE c.user_id = ?
       ORDER BY c.contact_name ASC`,
      [user_id]
    );

    return res.status(200).json(contacts);

  } catch (error) {
    console.error('Error fetching contacts:', error);
    return res.status(500).json({ 
      message: 'Failed to fetch contacts' 
    });
  }
}
