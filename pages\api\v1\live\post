import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db'; // Adjust the path according to your project structure

// Create a new stream
export async function POST(req: NextApiRequest) {
    try {
        const { user_id, title, thumbnail, room_name } = await req.json();

        // Validate input
        if (!user_id || !title || !room_name) {
            return new Response(JSON.stringify({ message: 'user_id, title, and room_name are required' }), { status: 400 });
        }

        const sql = `
            INSERT INTO streams (user_id, title, thumbnail, room_name)
            VALUES (?, ?, ?, ?)
        `;

        const result: any = await query(sql, [user_id, title, thumbnail, room_name]); // Cast result to any
        return new Response(JSON.stringify({ message: 'Stream created successfully', streamId: result.insertId }), { status: 201 });
    } catch (error: unknown) {
        return new Response(JSON.stringify({ message: 'Failed to create stream', error: (error as Error).message }), { status: 500 });
    }
}

// Retrieve all streams
export async function GET(req: NextApiRequest) {
    try {
        const sql = 'SELECT * FROM streams';
        const streams = await query(sql);
        return new Response(JSON.stringify(streams), { status: 200 });
    } catch (error: unknown) {
        return new Response(JSON.stringify({ message: 'Failed to retrieve streams', error: (error as Error).message }), { status: 500 });
    }
}
