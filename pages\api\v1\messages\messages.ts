import { NextApiRequest, NextApiResponse } from 'next';
import pusher from '@/lib/pusher';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { message } = req.body;

        // Trigger an event on Pusher
        pusher.trigger('chat', 'message-sent', {
            message,
        });

        return res.status(200).json({ success: true, message });
    }

    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
}
