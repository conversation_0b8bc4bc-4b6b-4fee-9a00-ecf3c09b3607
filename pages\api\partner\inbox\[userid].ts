import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

interface ConsultantDetails {
  id: string;
  name: string;
  email: string;
  pfp: string;
}

interface RequestDetails {
  status: string;
  chatRequestId: string;
  time: string;
  bel: string;
  roomN: string;
}

interface ResponseData {
  userId: string;
  consultantDetails: ConsultantDetails;
  userDetails: {
    name: string;
    email: string;
    pfp: string;
  };
  requestDetails: RequestDetails;
}

interface ChatRequest extends RowDataPacket {
  userId: string;
  status: string;
  userName: string;
  userEmail: string;
  userPfp: string;
  time: string;
  bel: string;
  roomN: string;
  id: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid || Array.isArray(userid)) {
    return res.status(400).json({ error: 'User ID is required and should be a string.' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    if (req.method === 'GET') {
      // Fetch consultant details
      const [consultantRows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT id, name, email, pfp FROM consultants WHERE id = ?',
        [userid]
      );

      if (consultantRows.length === 0) {
        return res.status(404).json({ message: 'Consultant not found' });
      }

      const consultant = consultantRows[0];

      // Fetch chat requests including both 'approved' and 'pending' statuses
      const [chatRequests]: [ChatRequest[], FieldPacket[]] = await connection.query(
        'SELECT cr.id, cr.userId, cr.status, cr.time, cr.bel, cr.roomN, u.username AS userName, u.email AS userEmail, u.pfp AS userPfp ' +
        'FROM chatRequests cr JOIN users u ON cr.userId = u.id ' +
        'WHERE cr.consultantId = ? AND (cr.status = ? OR cr.status = ?)', // Include 'pending' and 'approved' statuses
        [userid, 'pending', 'approved']
      );

      // Return an empty array if no chat requests found
      if (chatRequests.length === 0) {
        return res.status(200).json([]); // Empty array for no data
      }

      // Prepare the response data
      const detailedRequests: ResponseData[] = chatRequests.map(row => ({
        userId: row.userId,
        consultantDetails: {
          id: consultant.id,
          name: consultant.name,
          email: consultant.email,
          pfp: consultant.pfp,
        },
        userDetails: {
          name: row.userName,
          email: row.userEmail,
          pfp: row.userPfp,
        },
        requestDetails: {
          status: row.status,
          chatRequestId: row.id,
          time: row.time,
          bel: row.bel,
          roomN: row.roomN,
        }
      }));

      return res.status(200).json(detailedRequests);
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
