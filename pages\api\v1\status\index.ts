// pages/api/update.ts

import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';

interface MySQLRow {
  active: number;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { id, active } = req.body;

      // Validate the incoming data
      if (typeof id !== 'number' || (active !== 0 && active !== 1)) {
        return res.status(400).json({ message: 'Invalid id or active value' });
      }

      // Connect to the MySQL database
      const connection = await connectToDatabase();

      // Check if user exists with the provided id
      const [rows] = await connection.execute<MySQLRow[]>(
        'SELECT active FROM consultants WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Update the 'active' status in the database
      await connection.execute(
        'UPDATE consultants SET active = ? WHERE id = ?',
        [active, id]
      );

      // Return the updated active value
      res.status(200).json({  success: true, active });
    } catch (error) {
      console.error('Error updating value:', error);
      res.status(500).json({ message: 'Error updating value' });
    }
  } else {
    // Handle any other HTTP methods
    res.status(405).json({ message: 'Method Not Allowed' });
  }
}
