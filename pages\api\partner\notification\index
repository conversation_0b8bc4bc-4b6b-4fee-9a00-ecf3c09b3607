import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient, Prisma } from '@prisma/client';

// Initialize Prisma client
const prisma = new PrismaClient();

// Function to get expoPushToken and name for a userId from the consultants table
const getUserDetails = async (userId: string) => {
    try {
        console.log(`Fetching user details for userId: ${userId}`);

        // Convert userId to integer and fetch consultant details
        const consultant = await prisma.consultants.findUnique({
            where: { id: parseInt(userId, 10) }, // Convert string to integer
            select: { name: true, expoPushToken: true },
        });

        if (!consultant) {
            console.warn(`No consultant found for userId: ${userId}`);
            return null;
        }

        return consultant;
    } catch (error) {
        console.error('Database query error:', error);

        // Check if the error is from Prisma
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            console.error('Prisma error code:', error.code);
        }

        throw new Error('Database query failed');
    }
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { userId, body, data } = req.body;

        // Validate inputs
        if (!userId || isNaN(Number(userId))) {
            return res.status(400).json({ error: 'Valid userId (integer) is required' });
        }

        if (!body) {
            return res.status(400).json({ error: 'Notification body is required' });
        }

        try {
            // Fetch the consultant's details (name and push token)
            const consultant = await getUserDetails(userId);

            if (!consultant) {
                return res.status(404).json({ error: 'Consultant not found' });
            }

            if (!consultant.expoPushToken) {
                return res.status(400).json({ error: 'Consultant is missing a push token' });
            }

            // Send the push notification
            const response = await fetch('https://exp.host/--/api/v2/push/send', {
                method: 'POST',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    to: consultant.expoPushToken,
                    sound: 'default',
                    title: `Hello, ${consultant.name}!`, // Use consultant's name dynamically
                    body,
                    data,
                }),
            });

            const result = await response.json();
            if (!response.ok) {
                console.error('Failed to send push notification:', result);
                return res.status(response.status).json({ error: 'Push notification failed', details: result });
            }

            res.status(200).json({ success: true, result });
        } catch (error: any) {
            console.error('Error sending notification:', error.message);
            res.status(500).json({ error: 'Failed to send notification', details: error.message });
        }
    } else {
        // Method not allowed
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
