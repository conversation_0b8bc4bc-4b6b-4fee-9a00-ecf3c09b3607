import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise'; // Import RowDataPacket for type safety

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check if the request method is GET
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  let connection;
  try {
    // Create a database connection
    connection = await connectToDatabase();
    
    // Execute the query to fetch all active users
    const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query('SELECT * FROM consultants WHERE active = 1');

    // Check if no active users are found
    if (rows.length === 0) {
      return res.status(404).json({ message: 'No active users found' });
    }

    // Send the result back as JSON
    res.status(200).json(rows);
  } catch (error) {
    // Log the error and send a 500 response
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to fetch active users' });
  } finally {
    // Ensure the connection is closed
    if (connection) {
      await connection.end();
    }
  }
}
