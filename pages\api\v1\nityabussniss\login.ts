import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import bcrypt from 'bcrypt';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

interface Consultant extends RowDataPacket {
    id: number;
    password: string;
    name: string;
    pfp: string;
    push_token: string;
    number: string;
}

const loginHandler = async (req: NextApiRequest, res: NextApiResponse) => {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { email, password } = req.body;

    if (!email || !password) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    try {
        const db = await connectToDatabase();

        // Login directly with consultant's number
        const [rows]: [Consultant[], FieldPacket[]] = await db.query<Consultant[]>(
            'SELECT id, password, name, pfp, push_token, number FROM consultants WHERE number = ?',
            [email]
        );

        const consultant = rows[0];

        if (!consultant) {
            console.log('Consultant not found');
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        const isPasswordValid = await bcrypt.compare(password, consultant.password);

        if (!isPasswordValid) {
            console.log('Invalid consultant password');
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Successful login
        console.log('Consultant login successful');
        return res.status(200).json({
            message: 'Login successful',
            userId: consultant.id,
            user: {
                email: null,
                username: consultant.name,
                pfp: consultant.pfp,
                pushtoken: consultant.push_token,
                contact_no: consultant.number,
                consultant: true,
            },
        });

    } catch (error) {
        console.error('Error logging in:', error);
        return res.status(500).json({ message: 'Error logging in' });
    }
};

export default loginHandler;
