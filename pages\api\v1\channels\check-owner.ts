import { NextApiRequest, NextApiResponse } from 'next';
import Channel from '@/models/Channels';
import mongoose from 'mongoose';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const { channelId, consultantId } = req.query;

  if (!channelId || !consultantId) {
    return res.status(400).json({ error: 'Missing required parameters: channelId and consultantId' });
  }

  try {
    // Connect to MongoDB if not already connected
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI as string);
    }

    // Find the channel
    const channel = await Channel.findById(channelId);

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    // Check if consultant is the owner
    const isOwner = channel._id === consultantId;

    return res.status(200).json({ 
      isOwner,
      channelId,
      consultantId
    });
  } catch (error) {
    console.error('Error checking channel ownership:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}