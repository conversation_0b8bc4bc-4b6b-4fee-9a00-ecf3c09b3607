import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket, ResultSetHeader } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    // Handle GET request
    if (req.method === 'GET') {
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        `SELECT 
          id, 
          name, 
          per_minute_rate, 
          birth_date, 
          total_sales, 
          isLive, 
          password, 
          pfp, 
          TOTAL_REQUEST, 
          GENDER, 
          profile_url, 
          categories, 
          auth_check, 
          code, 
          number, 
          email, 
          verified_email, 
          categoriess, 
          exp, 
          isChatOn, 
          thumnel 
        FROM consultants 
        WHERE id = ?`, 
        [userid]
      );

      if (rows.length === 0) {
        return res.status(404).json({ message: 'User not found' });
      }

      return res.status(200).json(rows[0]);
    }

    // Handle PUT request
    if (req.method === 'PUT') {
      const {
        name,
        per_minute_rate,
        birth_date,
        total_sales,
        isLive,
        password,
        pfp,
        TOTAL_REQUEST,
        GENDER,
        profile_url,
        categories,
        auth_check,
        code,
        number,
        verified_email,
        categoriess,
        exp,
        isChatOn,
        thumnel,
      } = req.body;

      // Construct the update query with the fields provided
      const fieldsToUpdate = [];
      const values = [];

      // Add fields only if they are defined
      if (name !== undefined) {
        fieldsToUpdate.push('name = ?');
        values.push(name);
      }
      if (per_minute_rate !== undefined) {
        fieldsToUpdate.push('per_minute_rate = ?');
        values.push(per_minute_rate);
      }
      if (birth_date !== undefined) {
        fieldsToUpdate.push('birth_date = ?');
        values.push(birth_date);
      }
      if (total_sales !== undefined) {
        fieldsToUpdate.push('total_sales = ?');
        values.push(total_sales);
      }
      if (isLive !== undefined) {
        fieldsToUpdate.push('isLive = ?');
        values.push(isLive);
      }
      if (password !== undefined) {
        fieldsToUpdate.push('password = ?');
        values.push(password);
      }
      if (pfp !== undefined) {
        fieldsToUpdate.push('pfp = ?');
        values.push(pfp);
      }
      if (TOTAL_REQUEST !== undefined) {
        fieldsToUpdate.push('TOTAL_REQUEST = ?');
        values.push(TOTAL_REQUEST);
      }
      if (GENDER !== undefined) {
        fieldsToUpdate.push('GENDER = ?');
        values.push(GENDER);
      }
      if (profile_url !== undefined) {
        fieldsToUpdate.push('profile_url = ?');
        values.push(profile_url);
      }
      if (categories !== undefined) {
        fieldsToUpdate.push('categories = ?');
        values.push(categories);
      }
      if (auth_check !== undefined) {
        fieldsToUpdate.push('auth_check = ?');
        values.push(auth_check);
      }
      if (code !== undefined) {
        fieldsToUpdate.push('code = ?');
        values.push(code);
      }
      if (number !== undefined) {
        fieldsToUpdate.push('number = ?');
        values.push(number);
      }
      if (verified_email !== undefined) {
        fieldsToUpdate.push('verified_email = ?');
        values.push(verified_email);
      }
      if (categoriess !== undefined) {
        fieldsToUpdate.push('categoriess = ?');
        values.push(categoriess);
      }
      if (exp !== undefined) {
        fieldsToUpdate.push('exp = ?');
        values.push(exp);
      }
      if (isChatOn !== undefined) {
        fieldsToUpdate.push('isChatOn = ?');
        values.push(isChatOn);
      }
      if (thumnel !== undefined) {
        fieldsToUpdate.push('thumnel = ?');
        values.push(thumnel);
      }

      // Add userid to the values for the WHERE clause
      values.push(userid);

      // If no fields to update, return an error
      if (fieldsToUpdate.length === 0) {
        return res.status(400).json({ error: 'No fields to update' });
      }

      // Execute the update query
      const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
        `UPDATE consultants SET ${fieldsToUpdate.join(', ')} WHERE id = ?`,
        values
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'User not found or not updated' });
      }

      return res.status(200).json({ message: 'User updated successfully' });
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
