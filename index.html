<!DOCTYPE html>
<html>
<head>
    <title>Hindi Voice Agent Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        .green { background: #4CAF50; color: white; }
        .red { background: #f44336; color: white; }
        .blue { background: #2196F3; color: white; }
        .orange { background: #FF9800; color: white; }
        .input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            width: 200px;
            margin: 5px;
        }
        #status {
            margin: 20px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 5px;
            min-height: 50px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #cce7ff; color: #004085; border: 1px solid #b3d7ff; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🎤 Hindi Voice Agent Test Client</h2>
        <p>Connect to your Hindi voice agent and start speaking!</p>
        
        <div style="margin: 20px 0;">
            <input type="text" id="roomName" placeholder="Room name" value="test-room" class="input">
            <button onclick="connect()" class="button green" id="connectBtn">🚀 Connect to Agent</button>
            <button onclick="disconnect()" class="button red" id="disconnectBtn" disabled>❌ Disconnect</button>
        </div>
        
        <div id="status" class="info">Ready to connect...</div>
        
        <div style="margin: 20px 0;">
            <button onclick="toggleMic()" class="button blue" id="micBtn" disabled>🎤 Start Microphone</button>
            <button onclick="testSpeak()" class="button orange" id="testBtn" disabled>🗣️ Test Hindi</button>
        </div>
        
        <audio id="remoteAudio" autoplay controls style="width: 100%; margin-top: 20px;"></audio>
    </div>

    <!-- Fixed LiveKit Client Loading -->
    <script src="https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js"></script>
    
    <script>
        let room = null;
        let connected = false;
        let micEnabled = false;
        let localAudioTrack = null;

        const LIVEKIT_URL = 'wss://core.ask.nityasha.com';
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = type;
        }

        function generateToken() {
            return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTgzMDg4NDUsImlzcyI6InByb2RfbWVyY3VyeV9saXZla2l0XzIwMjUiLCJuYW1lIjoidXNlciIsIm5iZiI6MTc1ODMwNTI0NSwic3ViIjoidXNlciIsInZpZGVvIjp7InJvb20iOiJ0ZXNzc3Rzc2Fhc2Rkc3NzIiwicm9vbUpvaW4iOnRydWV9fQ.M5M-ByFJTDDrY6Owi7tA4osy1tUI5rtRMJALnmyglQE";
        }

        async function connect() {
            const roomName = document.getElementById('roomName').value;
            
            try {
                updateStatus('🔄 Connecting to Hindi Voice Agent...', 'info');
                
                // Use correct LiveKit API
                room = new LiveKit.Room({
                    audioCaptureDefaults: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                    },
                });

                room.on('connected', () => {
                    updateStatus('✅ Connected! Waiting for Hindi Voice Agent...', 'success');
                    connected = true;
                    updateButtons();
                });

                room.on('participantConnected', (participant) => {
                    console.log('Participant connected:', participant.identity);
                    updateStatus(`✅ ${participant.identity} joined the room`, 'success');
                    
                    if (participant.identity.toLowerCase().includes('agent')) {
                        updateStatus('🎤 Hindi Voice Agent is ready! Click "Start Microphone" to begin.', 'success');
                    }
                });

                room.on('trackSubscribed', (track, publication, participant) => {
                    if (track.kind === 'audio' && participant.identity.toLowerCase().includes('agent')) {
                        const audioElement = document.getElementById('remoteAudio');
                        track.attach(audioElement);
                        updateStatus('🔊 Hindi Voice Agent connected! Start speaking in Hindi or English.', 'success');
                        audioElement.style.display = 'block';
                    }
                });

                room.on('disconnected', () => {
                    connected = false;
                    updateButtons();
                    updateStatus('Disconnected from agent', 'info');
                });

                const token = generateToken();
                await room.connect(LIVEKIT_URL, token);

            } catch (error) {
                console.error('Connection error:', error);
                updateStatus(`❌ Connection failed: ${error.message}`, 'error');
            }
        }

        async function disconnect() {
            if (room) {
                await room.disconnect();
                room = null;
            }
            connected = false;
            micEnabled = false;
            updateButtons();
            updateStatus('Disconnected', 'info');
        }

        async function toggleMic() {
            if (!connected || !room) {
                updateStatus('❌ Please connect first', 'error');
                return;
            }

            try {
                if (!micEnabled) {
                    updateStatus('🎤 Requesting microphone access...', 'info');
                    
                    localAudioTrack = await LiveKit.createLocalAudioTrack({
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                    });
                    
                    await room.localParticipant.publishTrack(localAudioTrack);
                    micEnabled = true;
                    updateStatus('🎤 Microphone active! Start speaking to the Hindi Voice Agent.', 'success');
                    
                } else {
                    if (localAudioTrack) {
                        await room.localParticipant.unpublishTrack(localAudioTrack);
                        localAudioTrack.stop();
                        localAudioTrack = null;
                    }
                    micEnabled = false;
                    updateStatus('🔇 Microphone stopped', 'info');
                }
                
                updateButtons();
                
            } catch (error) {
                console.error('Microphone error:', error);
                updateStatus(`❌ Microphone error: ${error.message}`, 'error');
            }
        }

        function testSpeak() {
            updateStatus('🗣️ Try saying: "नमस्ते" or "Hello, how are you?" to test the agent!', 'info');
        }

        function updateButtons() {
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const micBtn = document.getElementById('micBtn');
            const testBtn = document.getElementById('testBtn');

            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            micBtn.disabled = !connected;
            testBtn.disabled = !connected;

            if (micEnabled) {
                micBtn.innerHTML = '🔇 Stop Microphone';
                micBtn.className = 'button red';
            } else {
                micBtn.innerHTML = '🎤 Start Microphone';
                micBtn.className = 'button blue';
            }
        }

        window.addEventListener('load', () => {
            updateStatus('Ready to connect to Hindi Voice Agent', 'info');
            updateButtons();
            
            if (typeof LiveKit === 'undefined') {
                updateStatus('❌ Error: LiveKit library failed to load. Please refresh the page.', 'error');
            } else {
                console.log('LiveKit loaded successfully');
            }
        });
    </script>
</body>
</html>
