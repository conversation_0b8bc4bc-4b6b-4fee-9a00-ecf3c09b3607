import { query } from '@/lib/db';

export default async function handler(req, res) {
    const { method } = req;

    if (method === 'POST') {
        const { userId, consultantId } = req.body;

        // Create a new chat request
        try {
            const result = await query(
                'INSERT INTO chatRequests (userId, consultantId, status) VALUES (?, ?, ?)',
                [userId, consultantId, 'pending']
            );
            res.status(201).json({ message: 'Chat request sent', requestId: result.insertId });
        } catch (error) {
            console.error('Error creating chat request:', error);
            res.status(500).json({ message: 'Error creating chat request' });
        }
    } else if (method === 'GET') {
        const { userId } = req.query;

        // Fetch pending requests for the user
        try {
            const requests = await query(
                'SELECT * FROM chatRequests WHERE userId = ? AND status = ?',
                [userId, 'pending']
            );
            res.status(200).json(requests);
        } catch (error) {
            console.error('Error fetching chat requests:', error);
            res.status(500).json({ message: 'Error fetching chat requests' });
        }
    } else if (method === 'PUT') {
        const { requestId, status } = req.body;

        // Update the status of a chat request
        try {
            await query(
                'UPDATE chatRequests SET status = ? WHERE id = ?',
                [status, requestId]
            );
            res.status(200).json({ message: 'Request status updated' });
        } catch (error) {
            console.error('Error updating chat request:', error);
            res.status(500).json({ message: 'Error updating chat request' });
        }
    } else {
        res.setHeader('Allow', ['POST', 'GET', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
}
