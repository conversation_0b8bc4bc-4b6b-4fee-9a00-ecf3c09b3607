import { query } from '@/lib/db';
import type { NextApiRequest, NextApiResponse } from 'next';

export async function POST(req: NextApiRequest, res: NextApiResponse) {
    const { channelId, userId } = await req.json();

    // Validate input
    if (!channelId || !userId) {
        return res.status(400).json({ message: 'channelId and userId are required' });
    }

    try {
        // Example: Logic to stop the stream, e.g., updating the database
        const sql = `
            UPDATE streams 
            SET stopped_at = NOW() 
            WHERE agora_channel_id = ? AND user_id = ?
        `;
        await query(sql, [channelId, userId]);

        return res.status(200).json({ message: 'Stream stopped successfully', channelId });
    } catch (error: unknown) {
        // Type assertion to Error
        const errorMessage = (error as Error).message || 'Unknown error';
        return res.status(500).json({ message: 'Failed to stop stream', error: errorMessage });
    }
}
