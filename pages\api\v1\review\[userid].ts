import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query; // Extract userid from the query parameters

  // Check if userid is provided for GET requests
  if (!userid && req.method === 'GET') {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;

  try {
    // Create a database connection
    connection = await connectToDatabase();

    if (req.method === 'GET') {
      // Handle GET request
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        `
        SELECT 
          r.review_id,
          r.consultantId,
          r.reviewer_name,
          r.rating,
          r.review_text,
          r.review_date,
          r.user_id,
          u.username,
          u.pfp,
          c.name
        FROM 
          Review r
        INNER JOIN 
          users u ON r.user_id = u.id
        INNER JOIN 
          consultants c ON r.consultantId = c.id
        WHERE 
          r.consultantId = ?
        `,
        [userid]
      );

      // Check if no reviews are found
      if (rows.length === 0) {
        return res.status(404).json({ message: 'No reviews found for this user' });
      }

      // Send all reviews with user details back as JSON
      return res.status(200).json(rows);
    } else if (req.method === 'POST') {
      // Handle POST request
      const { consultantId, reviewerName, rating, reviewText, userId } = req.body;

      // Validate input
      if (!consultantId || !reviewerName || !rating || !reviewText || !userId) {
        return res.status(400).json({ error: 'All fields are required' });
      }

      // Insert the new review into the database
      const [result]: [any, FieldPacket[]] = await connection.query(
        `
        INSERT INTO Review (consultantId, reviewer_name, rating, review_text, review_date, user_id)
        VALUES (?, ?, ?, ?, NOW(), ?)
        `,
        [consultantId, reviewerName, rating, reviewText, userId]
      );

      // Check if the review was successfully created
      if (result.affectedRows === 1) {
        return res.status(201).json({ message: 'Review created successfully' });
      } else {
        return res.status(500).json({ error: 'Failed to create review' });
      }
    } else {
      // Handle unsupported methods
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    // Log the error and send a 500 response
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    // Ensure the connection is closed
    if (connection) {
      await connection.end();
    }
  }
}
