import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';

// Fetch active consultants
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method Not Allowed' });
    }

    try {
        const db = await connectToDatabase();
        const [rows] = await db.execute(
            'SELECT id, name, pfp, per_minute_rate ,stream_key, stream_url FROM consultants WHERE stream_active = 1'
        );
        res.status(200).json(rows);
    } catch (error) {
        res.status(500).json({ message: 'Internal Server Error', error });
    }
}
