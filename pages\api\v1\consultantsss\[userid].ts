import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket, ResultSetHeader } from 'mysql2/promise';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query;

  if (!userid) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;

  try {
    connection = await connectToDatabase();

    if (req.method === 'GET') {
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT * FROM consultants WHERE id = ?',
        [userid]
      );

      if (rows.length === 0) {
        return res.status(404).json({ message: 'User not found' });
      }

      return res.status(200).json(rows[0]);
    }

if (req.method === 'PUT') {
  const { balance } = req.body;

  if (balance === undefined || balance === '' || isNaN(balance)) {
    return res.status(400).json({ error: 'Valid balance is required for update' });
  }

  // Ensure balance is a valid number before using it
  const numericBalance = parseFloat(balance);
  if (isNaN(numericBalance)) {
    return res.status(400).json({ error: 'Invalid balance value' });
  }

  const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
    'UPDATE consultants SET balance = balance + ? WHERE id = ?',
    [numericBalance, userid]
  );

  if (result.affectedRows === 0) {
    return res.status(404).json({ message: 'User not found or not updated' });
  }

  return res.status(200).json({ message: 'Balance incremented successfully' });
}


    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
