import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket, ResultSetHeader } from 'mysql2/promise'; // Import ResultSetHeader for type safety

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { userid } = req.query; // Extract userid from the query parameters

  // Check if userid is provided
  if (!userid) {
    return res.status(400).json({ error: 'User ID is required' });
  }

  let connection;
  
  try {
    // Create a database connection
    connection = await connectToDatabase();

    // Handle GET request
    if (req.method === 'GET') {
      // Execute the query to fetch the user by userid
      const [rows]: [RowDataPacket[], FieldPacket[]] = await connection.query('SELECT * FROM payments WHERE id = ?', [userid]);

      // Check if no user is found
      if (rows.length === 0) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Send the result back as JSON
      return res.status(200).json(rows[0]); // Return the first matching user
    }
    
    // Handle PUT request
    if (req.method === 'PUT') {
      const { balance } = req.body; // Extract fields to update from request body

      // Check if balance is provided in the request body
      if (balance === undefined) {
        return res.status(400).json({ error: 'Balance is required for update' });
      }

      // Execute the update query to modify the user's balance
      const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query('UPDATE users SET balance = ? WHERE id = ?', [balance, userid]);

      // Check if the user was updated
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'User not found or not updated' });
      }

      // Send success response
      return res.status(200).json({ message: 'User updated successfully' });
    }

    // If the method is not GET or PUT, return 405 Method Not Allowed
    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    // Log the error and send a 500 response
    console.error('Database query failed:', error);
    res.status(500).json({ error: 'Failed to process request' });
  } finally {
    // Ensure the connection is closed
    if (connection) {
      await connection.end();
    }
  }
}
