const bcrypt = require('bcrypt');

// Test password comparison function
const testPassword = async (password, hash) => {
    const match = await bcrypt.compare(password, hash);
    console.log(`Password match: ${match}`);
};

// Test with the hashed password from your logs
const hashFromLogs = '$2a$10$Rm38cuDuFclztv6vcdiYY.AlXLJv57nPAUgYngYd6ZnqGyFpGND1S'; // Example from the user

const testUserPassword = '123'; // This should be the password you're testing
testPassword(testUserPassword, hashFromLogs);
