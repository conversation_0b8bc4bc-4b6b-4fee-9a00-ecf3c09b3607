// lib/reminder-utils.ts
import type { z } from 'zod';

export interface ReminderInput {
    title: string;
    description?: string;
    reminder_date: string;
}

export interface ReminderToolResult {
    success: boolean;
    message?: string;
    id?: number;
    title?: string;
    description?: string;
    reminder_date?: string;
    display_date?: string;
    error?: string;
}

export interface ReminderRetryConfig {
    maxRetries: number;
    initialDelay: number;
    maxDelay: number;
}

export function isRetryableError(error: unknown): boolean {
    if (!error) return false;

    // Common retryable error codes
    const retryableCodes = [
        'ETIMEDOUT',
        'ECONNRESET',
        'ECONNREFUSED',
        'EPIPE',
        'ERR_STREAM_DESTROYED',
        'rate_limit_exceeded',
        'temporary_failure'
    ];

    const err = error as any;

    // If error has a retryable property, use that
    if (typeof err.retryable === 'boolean') {
        return err.retryable;
    }

    // Check error codes
    if (err.code && retryableCodes.includes(err.code)) {
        return true;
    }

    // Check error message for common retryable patterns
    const retryablePatterns = [
        /timeout/i,
        /temporarily unavailable/i,
        /rate limit/i,
        /too many requests/i,
        /try again/i,
        /connection (failed|reset)/i,
        /deadlock/i,
        /lock/i
    ];

    const errorMessage = err.message || '';
    return retryablePatterns.some(pattern => pattern.test(errorMessage));
}

export function sanitizeReminderInput(input: ReminderInput): ReminderInput {
    let safeTitle = sanitizeReminderTitle(input.title);
    if (!safeTitle || safeTitle.length === 0) {
        throw new Error('Title became empty after sanitization');
    }
    if (safeTitle.length > 255) {
        throw new Error('Title must be less than 255 characters');
    }

    let safeDesc = sanitizeReminderDescription(input.description || '');
    if (safeDesc.length > 1000) {
        throw new Error('Description must be less than 1000 characters');
    }

    // Parse and validate date
    const parsedDate = parseNaturalDate(input.reminder_date);
    if (!parsedDate) {
        throw new Error(`Could not understand the date "${input.reminder_date}". Please try formats like: "tomorrow 9 AM", "Sept 10 5 PM", or "2025-09-06 14:30:00"`);
    }

    // Check if date is in the future
    const reminderDateTime = new Date(parsedDate);
    const currentDateTime = new Date();
    if (reminderDateTime <= currentDateTime) {
        throw new Error(`The date "${parsedDate}" is in the past. Please choose a future date and time.`);
    }

    return {
        title: safeTitle,
        description: safeDesc,
        reminder_date: parsedDate
    };
}

export async function addReminderWithRetry(
    userId: number,
    input: ReminderInput, 
    retryConfig: ReminderRetryConfig,
    dbQuery: (sql: string, params: any[]) => Promise<any>
): Promise<ReminderToolResult> {
    
    const { maxRetries, initialDelay, maxDelay } = retryConfig;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            // Sanitize and validate input
            const safeInput = sanitizeReminderInput(input);

            // Try to insert the reminder
            const result = await dbQuery(
                'INSERT INTO reminders (user_id, title, description, reminder_date) VALUES (?, ?, ?, ?)',
                [userId, safeInput.title, safeInput.description, safeInput.reminder_date]
            );

            // Format date for display
            const displayDate = new Date(safeInput.reminder_date).toLocaleString('en-IN', {
                timeZone: 'Asia/Kolkata',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            // Success response
            return {
                success: true,
                id: result.insertId,
                title: safeInput.title,
                description: safeInput.description,
                reminder_date: safeInput.reminder_date,
                display_date: displayDate,
                message: `Reminder "${safeInput.title}" set for ${displayDate}`
            };

        } catch (error: any) {
            // Check if error is retryable
            const shouldRetry = isRetryableError(error) && attempt < maxRetries;
            if (!shouldRetry) {
                return {
                    success: false,
                    message: 'Failed to add reminder. Please try again.',
                    error: formatToolError(error)
                };
            }

            // Exponential backoff with jitter
            const delay = Math.min(initialDelay * Math.pow(2, attempt - 1), maxDelay) * (0.8 + Math.random() * 0.4);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
        }
    }

    return {
        success: false,
        message: 'Failed to add reminder after multiple attempts. Please try again.'
    };
}

function formatToolError(error: unknown): string {
    if (typeof error === 'string') return error;
    if (error instanceof Error) return error.message;
    if (error && typeof error === 'object' && 'message' in error) {
        return String((error as any).message);
    }
    return 'An unknown error occurred';
}

function sanitizeReminderTitle(input: string): string {
    if (!input || typeof input !== 'string') return '';

    let sanitized = input.trim();

    // Only remove dangerous script-related content, keep most characters
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    sanitized = sanitized.replace(/on\w+=/gi, '');
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitized = sanitized.replace(/\s+/g, ' ');

    return sanitized.substring(0, 255);
}

function sanitizeReminderDescription(input: string): string {
    if (!input || typeof input !== 'string') return '';

    let sanitized = input.trim();

    // Allow more characters in description but remove dangerous content
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    sanitized = sanitized.replace(/on\w+=/gi, '');
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitized = sanitized.replace(/\s+/g, ' ');

    return sanitized.substring(0, 1000);
}

function parseNaturalDate(dateString: string): string | null {
    if (!dateString || typeof dateString !== 'string') return null;

    try {
        const cleanDateString = dateString.trim();

        // Check for common date formats like "tomorrow 9 AM", "2025-09-06 14:30:00"
        const commonPatterns = [
            /^today\s+(\d{1,2}(?::\d{2})?)\s*(?:am|pm)?$/i,
            /^tomorrow\s+(\d{1,2}(?::\d{2})?)\s*(?:am|pm)?$/i,
            /^next\s+(\w+)\s+(\d{1,2}(?::\d{2})?)\s*(?:am|pm)?$/i,
            /^(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})$/,
            /^(\w+)\s+(\d{1,2})(?:st|nd|rd|th)?\s+(\d{1,2}(?::\d{2})?)\s*(?:am|pm)?$/i
        ];

        // If input exactly matches a standard format, use it directly
        if (/^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}$/.test(cleanDateString)) {
            return cleanDateString;
        }

        // Try to parse natural language dates
        let date = new Date(cleanDateString);

        // If parsing failed, try some common formats
        if (isNaN(date.getTime())) {
            // Try different formats
            const formats = [
                cleanDateString.replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/, '$3-$2-$1'), // DD/MM/YYYY to YYYY-MM-DD
                cleanDateString.replace(/(\d{1,2})-(\d{1,2})-(\d{4})/, '$3-$2-$1'), // DD-MM-YYYY to YYYY-MM-DD
            ];

            for (const format of formats) {
                date = new Date(format);
                if (!isNaN(date.getTime())) break;
            }
        }

        // If still invalid, return null
        if (isNaN(date.getTime())) return null;

        // Format to YYYY-MM-DD HH:MM:SS
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    } catch (error) {
        return null;
    }
}
