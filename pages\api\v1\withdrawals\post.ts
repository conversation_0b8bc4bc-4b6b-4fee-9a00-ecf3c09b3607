// pages/api/withdrawals.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db';
import { OkPacket, RowDataPacket } from 'mysql2'; // Adjust based on your library

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        // Create a new withdrawal request
        const { user_id, amount } = req.body;

        // Validate the request body
        if (!user_id || !amount) {
            return res.status(400).json({ error: 'User ID and amount are required' });
        }

        try {
            const sql = `
                INSERT INTO withdrawal_requests (user_id, amount)
                VALUES (?, ?)`;

            const result = await query(sql, [user_id, amount]) as OkPacket; // Type assertion

            return res.status(201).json({ id: result.insertId, user_id, amount, request_status: 'pending' });
        } catch (error) {
            console.error('Error creating withdrawal request:', error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    } else if (req.method === 'GET') {
        // Fetch all withdrawal requests
        try {
            const sql = `SELECT * FROM withdrawal_requests`;
            const results = await query(sql) as RowDataPacket[]; // Type assertion for row data

            return res.status(200).json(results);
        } catch (error) {
            console.error('Error fetching withdrawal requests:', error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    } else if (req.method === 'PUT') {
        // Update a withdrawal request status
        const { id, request_status } = req.body;

        // Validate the request body
        if (!id || !request_status) {
            return res.status(400).json({ error: 'ID and request status are required' });
        }

        try {
            const sql = `
                UPDATE withdrawal_requests
                SET request_status = ?
                WHERE id = ?`;

            const result = await query(sql, [request_status, id]) as OkPacket; // Type assertion

            if (result.affectedRows === 0) {
                return res.status(404).json({ error: 'Withdrawal request not found' });
            }

            return res.status(200).json({ id, request_status });
        } catch (error) {
            console.error('Error updating withdrawal request:', error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    } else if (req.method === 'DELETE') {
        // Delete a withdrawal request
        const { id } = req.body;

        // Validate the request body
        if (!id) {
            return res.status(400).json({ error: 'ID is required' });
        }

        try {
            const sql = `
                DELETE FROM withdrawal_requests
                WHERE id = ?`;

            const result = await query(sql, [id]) as OkPacket; // Type assertion

            if (result.affectedRows === 0) {
                return res.status(404).json({ error: 'Withdrawal request not found' });
            }

            return res.status(204).end(); // No content
        } catch (error) {
            console.error('Error deleting withdrawal request:', error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    } else {
        // Handle any other HTTP method
        res.setHeader('Allow', ['POST', 'GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
