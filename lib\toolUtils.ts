// lib/toolUtils.ts
import type { Tool, ToolCallOptions } from 'ai';
import { z } from 'zod';

/**
 * Creates a strongly-typed tool that can be used with the AI SDK
 */
export function createTool<TParams extends z.ZodObject<any>, TOutput>(
    description: string,
    parameters: TParams,
    execute: (input: z.infer<TParams>) => Promise<TOutput>
): Tool {
    return {
        description,
        inputSchema: parameters,
        execute: async (input: z.infer<TParams>, toolOptions: ToolCallOptions) => {
            try {
                return await execute(input);
            } catch (error) {
                console.error('Tool execution error:', error);
                throw error;
            }
        }
    };
}
