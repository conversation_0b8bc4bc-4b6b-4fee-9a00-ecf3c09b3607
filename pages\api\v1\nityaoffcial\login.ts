import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import bcrypt from 'bcrypt';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

interface OfficialAccount extends RowDataPacket {
    id: number;
    password: string;
    name: string;
    logo: string;
    push_token: string;
    contact_number: string;
}

const officialLoginHandler = async (req: NextApiRequest, res: NextApiResponse) => {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { number, password } = req.body;

    if (!number || !password) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    try {
        const db = await connectToDatabase();

        const [rows]: [OfficialAccount[], FieldPacket[]] = await db.query<OfficialAccount[]>(
            'SELECT id, password, name, logo, push_token, contact_number FROM official_accounts WHERE contact_number = ?',
            [number]
        );

        const official = rows[0];

        if (!official) {
            console.log('Official account not found');
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        const isPasswordValid = await bcrypt.compare(password, official.password);

        if (!isPasswordValid) {
            console.log('Invalid official account password');
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Successful login
        console.log('Official login successful');
        return res.status(200).json({
            message: 'Login successful',
            userId: official.id,
            Official: "True",
            user: {
                email: null,
                username: official.name,
                logo: official.logo,
                pushtoken: official.push_token,
                contact_no: official.contact_number,
                consultant: false,
            },
        });

    } catch (error) {
        console.error('Error logging in:', error);
        return res.status(500).json({ message: 'Error logging in' });
    }
};

export default officialLoginHandler;
