// Test script to demonstrate concurrent web crawling
// This is a standalone test to show how the concurrent crawling works

const { J<PERSON><PERSON> } = require('jsdom');

// Simulate the enhanced crawling function
async function crawlWebPage(url, timeoutMs = 10000) {
    const startTime = Date.now();
    console.log(`🌐 Starting crawl: ${url}`);
    
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; TestBot/1.0)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        const dom = new JSDOM(html);
        const title = dom.window.document.querySelector('title')?.textContent?.trim() || 'No title';
        
        const duration = Date.now() - startTime;
        console.log(`✅ Completed: ${url} - ${duration}ms - "${title.substring(0, 50)}..."`);
        
        return {
            success: true,
            title,
            content: html.substring(0, 500) + '...',
            duration
        };

    } catch (error) {
        const duration = Date.now() - startTime;
        console.log(`❌ Failed: ${url} - ${duration}ms - ${error.message}`);
        
        return {
            success: false,
            error: error.message,
            duration
        };
    }
}

// Enhanced concurrent crawling function
async function crawlMultipleWebPages(urls, maxConcurrent = 3, timeoutMs = 10000) {
    const startTime = Date.now();
    console.log(`\n🚀 Starting batch crawl of ${urls.length} URLs with max ${maxConcurrent} concurrent requests`);
    
    const results = [];
    const batches = [];
    
    // Create batches
    for (let i = 0; i < urls.length; i += maxConcurrent) {
        batches.push(urls.slice(i, i + maxConcurrent));
    }

    let urlIndex = 0;
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`📦 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} URLs)`);

        const batchPromises = batch.map(async (url, index) => {
            const currentIndex = urlIndex + index + 1;
            console.log(`🌐 [${currentIndex}/${urls.length}] Starting: ${url}`);
            
            const result = await crawlWebPage(url, timeoutMs);
            return { url, ...result };
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        urlIndex += batch.length;

        // Small delay between batches
        if (batchIndex < batches.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    const totalTime = Date.now() - startTime;
    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    const averageTime = results.reduce((sum, r) => sum + (r.duration || 0), 0) / results.length;

    // Performance report
    console.log(`\n🚀 Enhanced Parallel Web Crawler Performance Report:`);
    console.log(`📊 Total URLs: ${results.length}`);
    console.log(`✅ Successful: ${successful} (${Math.round((successful / results.length) * 100)}%)`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️ Total Time: ${totalTime}ms`);
    console.log(`⚡ Average Time per URL: ${Math.round(averageTime)}ms`);
    
    // Estimate time saved vs sequential processing
    const estimatedSequentialTime = results.reduce((sum, r) => sum + (r.duration || timeoutMs), 0);
    const timeSaved = Math.max(0, estimatedSequentialTime - totalTime);
    console.log(`🔥 Estimated time saved vs sequential: ${timeSaved}ms\n`);

    return {
        results,
        summary: {
            total: results.length,
            successful,
            failed,
            totalTime,
            averageTime: Math.round(averageTime),
            timeSaved
        }
    };
}

// Test function
async function testConcurrentCrawling() {
    console.log('🧪 Testing Concurrent Web Crawling\n');
    
    // Test URLs (using reliable, fast-loading sites)
    const testUrls = [
        'https://httpbin.org/delay/1',
        'https://httpbin.org/delay/2', 
        'https://httpbin.org/delay/1',
        'https://httpbin.org/delay/3',
        'https://httpbin.org/delay/2'
    ];

    try {
        const results = await crawlMultipleWebPages(testUrls, 3, 15000);
        
        console.log('📋 Detailed Results:');
        results.results.forEach((result, index) => {
            console.log(`${index + 1}. ${result.url}`);
            console.log(`   Status: ${result.success ? '✅ Success' : '❌ Failed'}`);
            console.log(`   Duration: ${result.duration}ms`);
            if (result.error) console.log(`   Error: ${result.error}`);
            console.log('');
        });

    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testConcurrentCrawling();
}

module.exports = { crawlMultipleWebPages, crawlWebPage };
