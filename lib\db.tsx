import mysql from 'mysql2/promise';

let pool: mysql.Pool;

async function createPool() {
    if (!pool) {
        pool = mysql.createPool({
           host: process.env.DB_HOST || 'mercury.nityasha.com',
            user: process.env.DB_USER || 'kzzuezbs_31aa9913123139jmasr',
            password: process.env.DB_PASSWORD || 'N4(I9_P9>!lPo:vmT0',
            database: process.env.DB_NAME || 'kzzuezbs_31aa9913123139jmasr',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
        });
    }
    return pool;
}

// Create a function to connect to the database
export async function connectToDatabase() {
    const db = await createPool();
    const connection = await db.getConnection();
    return connection;
}

// Export a query function
export async function query(sql: string, values?: unknown[]) {
    const connection = await connectToDatabase();
    try {
        const [results] = await connection.execute(sql, values);
        return results as unknown; // Cast to unknown or a more specific type if known
    } finally {
        await connection.release(); // Always release the connection
    }
}

// Optionally, you can test the connection when this module is loaded
(async () => {
    try {
        const db = await createPool();
        const connection = await db.getConnection();
        console.log('Database connection established');
        await connection.release();
    } catch (error) {
        console.error('Database connection failed:', error);
    }
})();
