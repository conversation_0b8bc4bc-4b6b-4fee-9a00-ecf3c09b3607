import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, ResultSetHeader, RowDataPacket } from 'mysql2/promise';

interface Consultant extends RowDataPacket {
  balance: number; // Only keeping the balance field
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  // Ensure consultant ID is provided
  if (!id) {
    return res.status(400).json({ error: 'Consultant ID is required' });
  }

  const consultantId = Array.isArray(id) ? id[0] : id;
  let connection;

  try {
    connection = await connectToDatabase();

    // Handle GET request
    if (req.method === 'GET') {
      // Specify the return type explicitly
      const [consultants]: [Consultant[], FieldPacket[]] = await connection.query<Consultant[]>(
        'SELECT balance FROM consultants WHERE id = ?',
        [consultantId]
      );

      if (consultants.length === 0) {
        return res.status(404).json({ message: 'Consultant not found' });
      }

      return res.status(200).json(consultants[0]); // Return the first consultant object
    }

    // Handle PUT request
    if (req.method === 'PUT') {
      const consultantData: Consultant = req.body; // Use the Consultant type for only the relevant fields
      const { balance } = consultantData; // Destructure the necessary fields

      const [result]: [ResultSetHeader, FieldPacket[]] = await connection.query(
        `UPDATE consultants SET 
          balance = ? 
        WHERE id = ?`, 
        [balance, consultantId]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'No consultant found or updated' });
      }

      return res.status(200).json({ message: 'Consultant updated successfully' });
    }

    // If method is neither GET nor PUT
    return res.status(405).json({ error: 'Method Not Allowed' });

  } catch (error) { 
    console.error('Database error:', error);
    res.status(500).json({ error: 'Database operation failed' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
