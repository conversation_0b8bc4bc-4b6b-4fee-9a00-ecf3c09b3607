import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import bcrypt from 'bcrypt';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';

interface User extends RowDataPacket {
    id: number;
    email?: string;
    password: string;
    username?: string;
    name?: string;
    pfp: string;
    pushtoken?: string;
    push_token?: string;
    contact_no?: string;
    number?: string;
}

const loginHandler = async (req: NextApiRequest, res: NextApiResponse) => {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
<<<<<<< HEAD
=======
    }

    const { email, password } = req.body;

    if (!email || !password) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    try {
        const db = await connectToDatabase();

        // Step 1: Check users by email or contact_no
        let [rows]: [User[], FieldPacket[]] = await db.query<User[]>(
            'SELECT id, email, password, username, pfp, pushtoken, contact_no FROM users WHERE email = ? OR contact_no = ?',
            [email, email]
        );

        let user = rows[0];

        if (user) {
            console.log('User found:', user);

            const isUserPasswordValid = await bcrypt.compare(password, user.password);
            if (isUserPasswordValid) {
                console.log('User password valid');
                return res.status(200).json({
                    message: 'Login successful',
                    userId: user.id,
                    user: {
                        email: user.email || null,
                        username: user.username,
                        pfp: user.pfp,
                        pushtoken: user.pushtoken,
                        contact_no: user.contact_no,
                        consultant: false,
                    },
                });
            }

            console.log('User password invalid — checking consultants fallback with number:', user.contact_no);

            if (!user.contact_no) {
                console.log('User contact_no is missing');
            }

            // Step 2: Try consultant login using contact_no
            [rows] = await db.query<User[]>(
                'SELECT id, password, name, pfp, push_token, number FROM consultants WHERE number = ?',
                [user.contact_no]
            );

            const consultant = rows[0];
            if (consultant) {
                console.log('Consultant found via user.contact_no:', consultant);

                const isConsultantPasswordValid = await bcrypt.compare(password, consultant.password);
                if (isConsultantPasswordValid) {
                    console.log('Consultant password valid via user fallback');
                    return res.status(200).json({
                        message: 'Login successful',
                        userId: consultant.id,
                        user: {
                            email: null,
                            username: consultant.name,
                            pfp: consultant.pfp,
                            pushtoken: consultant.push_token,
                            contact_no: consultant.number,
                            consultant: true,
                        },
                    });
                } else {
                    console.log('Consultant password invalid');
                }
            } else {
                console.log('Consultant not found by number');
            }
        } else {
            // Step 3: Direct consultant login by number
            console.log('No user found, trying direct consultant login by number:', email);
            [rows] = await db.query<User[]>(
                'SELECT id, password, name, pfp, push_token, number FROM consultants WHERE number = ?',
                [email]
            );

            const consultant = rows[0];
            if (consultant) {
                console.log('Consultant found directly:', consultant);

                const isConsultantPasswordValid = await bcrypt.compare(password, consultant.password);
                if (isConsultantPasswordValid) {
                    console.log('Consultant password valid');
                    return res.status(200).json({
                        message: 'Login successful',
                        userId: consultant.id,
                        user: {
                            email: null,
                            username: consultant.name,
                            pfp: consultant.pfp,
                            pushtoken: consultant.push_token,
                            contact_no: consultant.number,
                            consultant: true,
                        },
                    });
                } else {
                    console.log('Consultant password invalid');
                }
            } else {
                console.log('Consultant not found directly');
            }
        }

        return res.status(401).json({ message: 'Invalid credentials' });
    } catch (error) {
        console.error('Error logging in:', error);
        return res.status(500).json({ message: 'Error logging in' });
>>>>>>> 4474e097bae5127a9798351c8f5e26358dbc7989
    }

    const { email, password } = req.body;

    if (!email || !password) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    try {
        const db = await connectToDatabase();

        // Check user table for email or contact_no
        const [userRows]: [User[], FieldPacket[]] = await db.query<User[]>(
            'SELECT id, email, password, username, pfp, pushtoken, contact_no FROM users WHERE email = ? OR contact_no = ?',
            [email, email]
        );

        // Check consultant table by number
        const [consultantRows]: [User[], FieldPacket[]] = await db.query<User[]>(
            'SELECT id, password, name, pfp, push_token, number FROM consultants WHERE number = ?',
            [email]
        );

        const user = userRows[0];
        const consultant = consultantRows[0];

        console.log("User found: ", user);
        console.log("Consultant found: ", consultant);

        // First try user login
        if (user) {
            console.log('Comparing user password...');
            console.log("Input password:", password);
            console.log("User password hash:", user.password);
            console.log("Input password length:", password.length);
            console.log("User password hash length:", user.password.length);
            
            const isUserPasswordValid = await bcrypt.compare(password.trim(), user.password);
            if (isUserPasswordValid) {
                console.log('User password valid');
                return res.status(200).json({
                    message: 'Login successful',
                    userId: user.id,
                    user: {
                        email: user.email || null,
                        username: user.username,
                        pfp: user.pfp,
                        pushtoken: user.pushtoken,
                        contact_no: user.contact_no,
                        consultant: false,
                    },
                });
            } else {
                console.log('User password invalid');
            }
        }

        // Then try consultant login
        if (consultant) {
            console.log('Comparing consultant password...');
            console.log("Input password:", password);
            console.log("Consultant password hash:", consultant.password);
            console.log("Input password length:", password.length);
            console.log("Consultant password hash length:", consultant.password.length);

            const isConsultantPasswordValid = await bcrypt.compare(password.trim(), consultant.password);
            if (isConsultantPasswordValid) {
                console.log('Consultant password valid');
                return res.status(200).json({
                    message: 'Login successful',
                    userId: consultant.id,
                    user: {
                        email: null,
                        username: consultant.name,
                        pfp: consultant.pfp,
                        pushtoken: consultant.push_token,
                        contact_no: consultant.number,
                        consultant: true,
                    },
                });
            } else {
                console.log('Consultant password invalid');
            }
        }

        // If neither matched
        console.log('No matching credentials found');
        return res.status(401).json({ message: 'Invalid credentials' });

    } catch (error) {
        console.error('Error logging in:', error);
        return res.status(500).json({ message: 'Error logging in' });
    }
};

export default loginHandler;
