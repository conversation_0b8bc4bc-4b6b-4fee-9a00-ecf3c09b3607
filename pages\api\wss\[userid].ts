import { NextApiRequest, NextApiResponse } from 'next';
import { connectToDatabase } from '@/lib/db';
import { FieldPacket, RowDataPacket } from 'mysql2/promise';
import { WebSocket, WebSocketServer } from 'ws';
import type { Server as HTTPServer } from 'http';
import type { Socket } from 'net';

interface ChatRequest extends RowDataPacket {
  id: string;
  userId: string;
  status: string;
  time: Date;
  bel: string;
  roomN: string;
  userName: string;
  userEmail: string;
  userPfp: string;
}

interface WebSocketNextApiResponse extends NextApiResponse {
  socket: Socket & {
    server: HTTPServer & {
      ws: WebSocketServer;
    };
  };
}

const wss = new WebSocketServer({ noServer: true });

// Store active connections
const clients = new Map<string, WebSocket>();

const handler = async (req: NextApiRequest, res: WebSocketNextApiResponse) => {
  const { userid } = req.query;

  if (!userid || Array.isArray(userid)) {
    return res.status(400).json({ error: 'User ID is required and should be a string.' });
  }

  // Handle WebSocket upgrade
  if (req.headers.upgrade?.toLowerCase() === 'websocket') {
    if (!res.socket) {
      res.status(500).json({ error: 'Failed to setup WebSocket connection' });
      return;
    }

    // Store the WebSocket server instance
    if (!res.socket.server.ws) {
      res.socket.server.ws = wss;
    }

    wss.on('connection', (ws: WebSocket) => {
      // Store the client connection
      clients.set(userid as string, ws);

      ws.on('message', async (message: string) => {
        try {
          const data = JSON.parse(message.toString());
          // Handle different message types
          switch (data.type) {
            case 'ping':
              ws.send(JSON.stringify({ type: 'pong' }));
              break;
            case 'status_update':
              // Broadcast status update to all connected clients
              wss.clients.forEach((client) => {
                if (client !== ws && client.readyState === WebSocket.OPEN) {
                  client.send(JSON.stringify(data));
                }
              });
              break;
            default:
              ws.send(JSON.stringify({ error: 'Unknown message type' }));
          }
        } catch (error) {
          ws.send(JSON.stringify({ error: 'Invalid message format' }));
        }
      });

      ws.on('close', () => {
        clients.delete(userid as string);
        console.log(`Client ${userid} disconnected`);
      });

      ws.on('error', (error) => {
        console.error(`WebSocket error for client ${userid}:`, error); // Log the error
        clients.delete(userid as string);
      });      

    });

    res.socket.server.ws.handleUpgrade(req, res.socket, Buffer.alloc(0), (ws) => {
      res.socket.server.ws.emit('connection', ws, req);
    });

    return res.end();
  }

  // Handle HTTP requests
  let connection;
  try {
    connection = await connectToDatabase();

    if (req.method === 'GET') {
      const [consultantRows]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT id, name, email, pfp FROM consultants WHERE id = ?',
        [userid]
      );

      if (consultantRows.length === 0) {
        return res.status(404).json({ message: 'Consultant not found' });
      }

      const consultant = consultantRows[0];
      const [chatRequests]: [ChatRequest[], FieldPacket[]] = await connection.query(
        'SELECT cr.id, cr.userId, cr.status, cr.time, cr.bel, cr.roomN, u.username AS userName, u.email AS userEmail, u.pfp AS userPfp FROM chatRequests cr JOIN users u ON cr.userId = u.id WHERE cr.consultantId = ? AND cr.status != ?',
        [userid, 'approved']
      );

      const detailedRequests = chatRequests.map(row => ({
        userId: row.userId,
        consultantDetails: {
          id: consultant.id,
          name: consultant.name,
          email: consultant.email,
          pfp: consultant.pfp,
        },
        userDetails: {
          name: row.userName,
          email: row.userEmail,
          pfp: row.userPfp,
        },
        requestDetails: {
          status: row.status,
          chatRequestId: row.id,
          time: row.time,
          bel: row.bel,
          roomN: row.roomN,
        }
      }));

      return res.status(200).json(detailedRequests);
    }

    if (req.method === 'POST') {
      if (!req.body) {
        return res.status(400).json({ error: 'Request body is required' });
      }

      const { chatRequestId } = req.body as { chatRequestId: number };

      if (!chatRequestId) {
        return res.status(400).json({ error: 'Chat Request ID is required' });
      }

      const [existingChatRequest]: [RowDataPacket[], FieldPacket[]] = await connection.query(
        'SELECT * FROM chatRequests WHERE id = ? AND consultantId = ?',
        [chatRequestId, userid]
      );

      if (existingChatRequest.length === 0) {
        return res.status(404).json({ error: 'Chat request not found or does not belong to this consultant' });
      }

      await connection.query(
        'UPDATE chatRequests SET status = ? WHERE id = ?',
        ['approved', chatRequestId]
      );

      // Notify all connected WebSocket clients about the approval
      const notificationData = {
        type: 'chatApproved',
        chatRequestId,
        timestamp: new Date().toISOString()
      };

      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify(notificationData));
        }
      });

      return res.status(200).json({ 
        message: 'Chat request approved successfully',
        ...notificationData
      });
    }

    return res.status(405).json({ error: 'Method Not Allowed' });
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

export default handler;

export const config = {
  api: {
    bodyParser: true, // Enable body parser for POST requests
  },
};
