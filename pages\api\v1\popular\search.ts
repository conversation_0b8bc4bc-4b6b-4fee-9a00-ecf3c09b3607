import { connectToDatabase } from '@/lib/db';

export default async function handler(req, res) {
    try {
        const db = await connectToDatabase();

        if (req.method === 'POST') {
            // 🔹 Record a search
            const { searchTerm } = req.body;
            if (!searchTerm) {
                return res.status(400).json({ message: 'Search Term is required' });
            }

            // Insert or update search count
            await db.execute(
                `INSERT INTO popular_searches (search_term, search_count) 
                 VALUES (?, 1) 
                 ON DUPLICATE KEY UPDATE search_count = search_count + 1, last_searched = CURRENT_TIMESTAMP`,
                [searchTerm]
            );

            return res.status(200).json({ message: 'Search recorded successfully' });
        } 
        
        else if (req.method === 'GET') {
            // 🔹 Fetch popular searches
            const { limit } = req.query;
            const [popular] = await db.execute(
                `SELECT search_term, search_count 
                 FROM popular_searches 
                 ORDER BY search_count DESC 
                 LIMIT ?`,
                [parseInt(limit) || 10]
            );

            return res.status(200).json({ searches: popular });
        } 
        
        else {
            return res.status(405).json({ message: 'Method Not Allowed' });
        }
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Database error', error: error.message });
    }
}
