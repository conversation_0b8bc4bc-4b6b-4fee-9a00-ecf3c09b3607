import { NextRequest } from 'next/server';
import { google } from '@ai-sdk/google';
import { generateText, tool } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';
import { query } from '@/lib/db';
import { JSD<PERSON> } from 'jsdom'; // npm install jsdom @types/jsdom

export const maxDuration = 60;
export const dynamic = 'force-dynamic';

let redis: any = null;

// **EMOJI FILTERING FUNCTIONS**
function removeEmojis(str: string): string {
    if (!str || typeof str !== 'string') return '';

    // Comprehensive regex for all emojis, including sequences and modifiers
    // Based on Unicode Emoji standards; covers single emojis, ZWJ sequences, etc.
    const emojiRegex = /\p{Emoji_Presentation}|\p{Emoji}\uFE0F|\p{Emoji_Modifier_Base}|\p{Emoji_Modifier}|\p{Extended_Pictographic}|\p{Emoji_Component}|[\u{1F1E6}-\u{1F1FF}]/gu;  // Flags: g for global, u for Unicode

    let cleaned = str
        // Remove emojis using the comprehensive pattern
        .replace(emojiRegex, '')
        // Remove joiners and modifiers (e.g., zero-width joiners in family emojis)
        .replace(/[\u200D\uFE0F]/g, '')
        // Clean up extra spaces
        .replace(/\s+/g, ' ')
        .trim();

    // Safeguard: If filtered result is too short or empty, return original
    if (cleaned.length < 5 || cleaned.trim() === '') {
        return str.trim(); // Fallback to original (or a default message)
    }

    return cleaned;
}

function filterEmojiFromResponse(response: string): string {
    return removeEmojis(response);
}

// **INPUT FILTERING FUNCTIONS**
function sanitizeInput(input: string, allowSpecialChars: boolean = false): string {
    if (!input || typeof input !== 'string') return '';

    let sanitized = input.trim();

    if (!allowSpecialChars) {
        sanitized = sanitized.replace(/[%*^&<>{}[\]\\|`~]/g, '');
        sanitized = sanitized.replace(/\s+/g, ' ');
        sanitized = sanitized.replace(/^[^\w\s.,!?-]+|[^\w\s.,!?-]+$/g, '');
    }

    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    sanitized = sanitized.replace(/on\w+=/gi, '');

    return sanitized;
}

function validateAndSanitizeMessage(message: string): { isValid: boolean; sanitized: string; errors: string[] } {
    const errors: string[] = [];

    if (!message || typeof message !== 'string') {
        errors.push('Message must be a valid string');
        return { isValid: false, sanitized: '', errors };
    }

    if (message.length > 5000) {
        errors.push('Message exceeds maximum length of 5000 characters');
    }

    const suspiciousPatterns = [
        /(<script|<\/script>)/gi,
        /(javascript:|vbscript:)/gi,
        /(\bSELECT\b|\bUNION\b|\bDROP\b|\bDELETE\b|\bINSERT\b|\bUPDATE\b)/gi
    ];

    for (const pattern of suspiciousPatterns) {
        if (pattern.test(message)) {
            errors.push('Message contains potentially harmful content');
            break;
        }
    }

    const sanitized = sanitizeInput(message, true);

    if (sanitized.length === 0 && message.length > 0) {
        errors.push('Message became empty after sanitization');
    }

    return {
        isValid: errors.length === 0,
        sanitized,
        errors
    };
}

function sanitizeSearchQuery(query: string): string {
    if (!query) return '';
    let sanitized = query.trim();
    sanitized = sanitized.replace(/[&<>{}[\]\\|`]/g, '');
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    return sanitized.substring(0, 200);
}

function sanitizeDatabaseInput(input: string): string {
    if (!input) return '';
    return sanitizeInput(input, false).substring(0, 1000);
}

function sanitizeBase64Image(input: string): { isValid: boolean; dataUrl: string; errors: string[] } {
    const errors: string[] = [];

    if (!input || typeof input !== 'string') {
        errors.push('Image must be a valid string');
        return { isValid: false, dataUrl: '', errors };
    }

    // Check if it's a valid data URL
    const match = input.match(/^data:image\/(jpeg|png|gif);base64,(.+)$/);
    if (!match) {
        errors.push('Invalid image data URL format');
        return { isValid: false, dataUrl: '', errors };
    }
    const format = match[1];
    const base64 = match[2];
    const dataUrl = `data:image/${format};base64,${base64}`;

    // Basic length check (arbitrary limit for safety)
    if (base64.length > 5 * 1024 * 1024) { // 5MB limit
        errors.push('Image size exceeds maximum limit');
    }

    // Simple base64 validation
    if (!/^[A-Za-z0-9+/=]+$/.test(base64)) {
        errors.push('Invalid base64 encoding');
    }

    return {
        isValid: errors.length === 0,
        dataUrl,
        errors
    };
}

// **WEB CRAWLING FUNCTIONS**
async function crawlWebPage(url: string): Promise<{ success: boolean; content?: string; error?: string; title?: string; }> {
    try {
        const urlObj = new URL(url);
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
            return { success: false, error: 'Invalid URL protocol' };
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000);

        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; NityashaBot/1.0; +https://nityasha.com/bot)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
        }

        const contentType = response.headers.get('content-type') || '';
        if (!contentType.includes('text/html')) {
            return { success: false, error: 'Not an HTML page' };
        }

        const html = await response.text();
        const dom = new JSDOM(html);
        const document = dom.window.document;

        const unwantedElements = document.querySelectorAll('script, style, nav, header, footer, aside, .advertisement, .ads, .sidebar');
        unwantedElements.forEach(element => element.remove());

        const title = document.querySelector('title')?.textContent?.trim() || '';

        let content = '';
        const contentSelectors = [
            'article',
            'main',
            '[role="main"]',
            '.content',
            '.main-content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '#content',
            '#main',
            '.container',
            'body'
        ];

        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                content = element.textContent || '';
                if (content.length > 200) break;
            }
        }

        content = content
            .replace(/\s+/g, ' ')
            .replace(/\n\s*\n/g, '\n')
            .trim();

        if (content.length > 10000) {
            content = content.substring(0, 10000) + '...';
        }

        const sanitizedContent = sanitizeInput(content, true);
        const sanitizedTitle = sanitizeInput(title, true);

        if (!sanitizedContent || sanitizedContent.length < 100) {
            return { success: false, error: 'No meaningful content extracted' };
        }

        return {
            success: true,
            content: sanitizedContent,
            title: sanitizedTitle
        };

    } catch (error) {
        console.error('Crawling error for', url, ':', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown crawling error'
        };
    }
}

async function performGoogleSearchWithCrawling(searchQuery: string, crawlPages: boolean = true, maxCrawlPages: number = 3) {
    try {
        if (!searchQuery || typeof searchQuery !== 'string' || searchQuery.trim() === '' || searchQuery === 'undefined') {
            console.warn('❌ Invalid search query:', searchQuery);
            return {
                error: 'Invalid or empty search query provided',
                query: searchQuery,
                results: [],
                crawledContent: []
            };
        }
        const sanitizedQuery = sanitizeSearchQuery(searchQuery);
        if (!sanitizedQuery) {
            return {
                error: 'Search query became empty after sanitization',
                query: searchQuery,
                results: [],
                crawledContent: []
            };
        }

        const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
        const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;
        if (!apiKey || !searchEngineId) {
            return {
                error: 'Google Search API not configured',
                query: sanitizedQuery,
                results: [],
                crawledContent: []
            };
        }

        console.log('🔍 Performing search for:', sanitizedQuery);

        const response = await fetch(
            `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(sanitizedQuery)}&num=8`
        );

        if (!response.ok) {
            throw new Error(`Search API error: ${response.status}`);
        }

        const data = await response.json();

        const searchResults = data.items?.slice(0, 8).map((item: any) => ({
            title: sanitizeInput(item.title || '', true),
            snippet: sanitizeInput(item.snippet || '', true),
            link: item.link
        })) || [];

        if (!crawlPages || searchResults.length === 0) {
            return {
                query: sanitizedQuery,
                results: searchResults,
                totalResults: searchResults.length,
                crawledContent: []
            };
        }

        const crawlPromises = searchResults
            .slice(0, Math.min(maxCrawlPages, 5))
            .map(async (result) => {
                try {
                    const crawlResult = await crawlWebPage(result.link);
                    return {
                        ...result,
                        crawled: crawlResult.success,
                        fullContent: crawlResult.content || '',
                        crawlError: crawlResult.error,
                        extractedTitle: crawlResult.title || result.title
                    };
                } catch (error) {
                    return {
                        ...result,
                        crawled: false,
                        fullContent: '',
                        crawlError: 'Crawling failed',
                        extractedTitle: result.title
                    };
                }
            });

        const crawledResults = await Promise.all(crawlPromises);

        return {
            query: sanitizedQuery,
            results: searchResults,
            totalResults: searchResults.length,
            crawledContent: crawledResults,
            crawledSuccessfully: crawledResults.filter(r => r.crawled).length
        };

    } catch (error) {
        console.error('Google search with crawling error:', error);
        return {
            error: 'Search temporarily unavailable',
            query: searchQuery,
            results: [],
            crawledContent: []
        };
    }
}

// **REDIS AND UTILITY FUNCTIONS**
async function getRedisClient() {
    if (!redis) {
        redis = createClient({
            socket: {
                host: 'mercury.nityasha.com',
                port: 26739,
            },
            password: 'Amber@!23',
        });
        redis.on('error', (err: unknown) => console.error('Redis error', err));
        await redis.connect();
    }
    return redis;
}

function historyKey(id: string) {
    return `chat:${sanitizeInput(id)}:messages`;
}

function getCurrentISTTime() {
    return new Date().toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }).replace(/(\d{2})\/(\d{2})\/(\d{4}), (\d{2}):(\d{2}):(\d{2})/, '$3-$2-$1 $4:$5:$6');
}

async function checkPendingReminders(userId: number) {
    try {
        const currentTime = getCurrentISTTime();
        const reminders = await query(
            `SELECT * FROM reminders 
             WHERE user_id = ? AND is_completed = 0 AND reminder_date <= ? 
             ORDER BY reminder_date ASC`,
            [userId, currentTime]
        ) as any[];
        return reminders;
    } catch (error) {
        console.error('Error checking pending reminders:', error);
        return [];
    }
}

function normalizeMessage(message: any) {
    if (!message || !message.role) {
        return null;
    }

    if (typeof message.content === 'string') {
        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: message.content,
        };
    } else if (Array.isArray(message.content)) {
        // Preserve the full array for multimodal content (FIX: Do not strip images)
        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: message.content, // Keep original array including images
        };
    } else if (message.parts && Array.isArray(message.parts)) {
        const textContent = message.parts
            .filter((part: any) => part && part.type === 'text')
            .map((part: any) => part.text)
            .join(' ');

        return {
            id: message.id || crypto.randomUUID(),
            role: message.role,
            content: textContent || 'Empty message',
        };
    }

    return {
        id: message.id || crypto.randomUUID(),
        role: message.role,
        content: String(message.content || message.parts || ''),
    };
}

async function loadHistory(id: string) {
    try {
        const client = await getRedisClient();
        const raw = await client.get(historyKey(id));
        const parsed = raw ? JSON.parse(raw) : [];

        if (!Array.isArray(parsed)) {
            console.warn('Invalid history format, returning empty array');
            return [];
        }

        return parsed.filter(msg => msg).map(normalizeMessage).filter(msg => msg !== null);
    } catch (error) {
        console.error('Failed to load history:', error);
        return [];
    }
}

async function saveHistory(id: string, messages: any[]) {
    try {
        const client = await getRedisClient();
        const normalized = messages.map(normalizeMessage).filter(msg => msg !== null);
        await client.set(historyKey(id), JSON.stringify(normalized));
    } catch (error) {
        console.error('Failed to save history:', error);
    }
}

async function clearHistory(id: string) {
    try {
        const client = await getRedisClient();
        await client.del(historyKey(id));
    } catch (error) {
        console.error('Failed to clear history:', error);
    }
}

// **ENHANCED TOOLS WITH BETTER PARAMETER HANDLING**
function createToolsWithUserId(currentUserId: number) {
    return {
        search_tool: tool({
            description: 'Search Google for current information and crawl web pages for detailed content. Use this tool whenever the user asks about news, current events, weather, recent information, or any topic that requires up-to-date data from the web.',
            parameters: z.object({
                query: z.string().describe('The search query based on what the user is asking for. Extract keywords from the user message. For example: if user says "latest news of india" use "latest news india 2025", if user asks about weather use "weather [location] today"'),
                crawl_pages: z.boolean().optional().default(true).describe('Whether to crawl and extract full content from top search results'),
                max_crawl_pages: z.number().min(1).max(5).optional().default(3).describe('Maximum number of pages to crawl (1-5)')
            }),
            execute: async ({ query, crawl_pages, max_crawl_pages }) => {
                console.log('🔧 Tool called with params:', {
                    query,
                    query_type: typeof query,
                    query_length: query?.length,
                    crawl_pages,
                    max_crawl_pages
                });

                // Moved validations here to avoid schema issues
                if (!query || typeof query !== 'string' || query.trim() === '' || query === 'undefined') {
                    console.error('❌ Invalid search query received in tool execution:', {
                        query,
                        type: typeof query,
                        trimmed: query?.trim?.(),
                        length: query?.length
                    });
                    return {
                        error: 'Invalid search query provided to tool. Query must be a non-empty string.',
                        query: query,
                        results: [],
                        crawledContent: []
                    };
                }

                if (query.length > 200) {
                    console.warn('⚠️ Query too long, truncating');
                    query = query.substring(0, 200);  // Fixed: Added comma and removed 'legitimately'
                }

                console.log(`🔍 Searching for: "${query}" with crawling: ${crawl_pages}`);
                const sanitizedQuery = sanitizeSearchQuery(query);
                const limitedCrawlPages = Math.min(Math.max(max_crawl_pages || 3, 1), 5);
                const result = await performGoogleSearchWithCrawling(sanitizedQuery, crawl_pages, limitedCrawlPages);

                if (result.error) {
                    console.error('❌ Search error:', result.error);
                    return { ...result, note: 'Search failed; consider retrying with a refined query.' };
                }
                console.log(`✅ Search completed: ${result.crawledSuccessfully || 0} pages crawled, ${result.results?.length || 0} results`);
                return result;
            },
        }),
        crawl_website: tool({
            description: 'Crawl a specific website URL to extract its full content. Use this when you have a specific URL to analyze.',
            parameters: z.object({
                url: z.string().url("Must be a valid URL").describe('The URL to crawl and extract content from'),
            }),
            execute: async ({ url }) => {
                console.log(`🕷️ Crawling: ${url}`);
                const sanitizedUrl = sanitizeInput(url, true);
                if (!sanitizedUrl.startsWith('http://') && !sanitizedUrl.startsWith('https://')) {
                    return { success: false, error: 'Invalid URL format' };
                }
                const result = await crawlWebPage(sanitizedUrl);
                console.log(`✅ Crawl completed: ${result.success ? 'Success' : result.error}`);
                return result;
            },
        }),

        get_current_weather: tool({
            description: 'Get current weather for a location',
            parameters: z.object({
                location: z.string().min(1, "Location cannot be empty").describe('City and region/country'),
                unit: z.enum(['celsius', 'fahrenheit']).optional().default('celsius'),
            }),
            execute: async ({ location, unit }) => {
                const sanitizedLocation = sanitizeInput(location, true);
                const temperature = unit === 'fahrenheit' ? 86 : 30;
                return {
                    location: sanitizedLocation,
                    unit,
                    temperature,
                    conditions: 'Sunny',
                    humidity: '65%',
                    windSpeed: '10 km/h'
                };
            },
        }),

        check_pending_reminders: tool({
            description: 'Check for pending reminders that need user attention',
            parameters: z.object({}),
            execute: async () => {
                try {
                    const pendingReminders = await checkPendingReminders(currentUserId);
                    return {
                        success: true,
                        pendingCount: pendingReminders.length,
                        reminders: pendingReminders
                    };
                } catch (error) {
                    return { success: false, message: 'Failed to check reminders' };
                }
            },
        }),

        add_reminder: tool({
            description: 'Add a new reminder for the user',
            parameters: z.object({
                title: z.string().min(1, "Title cannot be empty").describe('Reminder title'),
                description: z.string().optional().describe('Reminder description'),
                reminder_date: z.string().describe('Reminder date and time in YYYY-MM-DD HH:MM:SS format (IST)'),
            }),
            execute: async ({ title, description, reminder_date }) => {
                try {
                    const sanitizedTitle = sanitizeDatabaseInput(title);
                    const sanitizedDescription = sanitizeDatabaseInput(description || '');
                    const sanitizedDate = sanitizeInput(reminder_date);

                    if (!sanitizedTitle) {
                        return { success: false, message: 'Invalid title after sanitization' };
                    }

                    const result = await query(
                        'INSERT INTO reminders (user_id, title, description, reminder_date) VALUES (?, ?, ?, ?)',
                        [currentUserId, sanitizedTitle, sanitizedDescription, sanitizedDate]
                    ) as any;
                    return {
                        success: true,
                        id: result.insertId,
                        title: sanitizedTitle,
                        reminder_date: sanitizedDate
                    };
                } catch (error) {
                    return { success: false, message: 'Failed to add reminder' };
                }
            },
        }),

        get_reminders: tool({
            description: 'Get all reminders for the user',
            parameters: z.object({
                completed: z.boolean().optional().describe('Filter by completion status'),
                upcoming_only: z.boolean().optional().describe('Show only upcoming reminders'),
            }),
            execute: async ({ completed, upcoming_only }) => {
                try {
                    let sql = 'SELECT * FROM reminders WHERE user_id = ?';
                    const params: any[] = [currentUserId];

                    if (completed !== undefined) {
                        sql += ' AND is_completed = ?';
                        params.push(completed);
                    }

                    if (upcoming_only) {
                        sql += ' AND reminder_date > ?';
                        params.push(getCurrentISTTime());
                    }

                    sql += ' ORDER BY reminder_date ASC';

                    const reminders = await query(sql, params);
                    return {
                        success: true,
                        reminders,
                        currentTime: getCurrentISTTime()
                    };
                } catch (error) {
                    return { success: false, message: 'Failed to get reminders' };
                }
            },
        }),

        update_reminder: tool({
            description: 'Update a reminder',
            parameters: z.object({
                reminder_id: z.number().positive("Reminder ID must be positive").describe('Reminder ID to update'),
                title: z.string().optional().describe('New title'),
                description: z.string().optional().describe('New description'),
                reminder_date: z.string().optional().describe('New reminder date in YYYY-MM-DD HH:MM:SS format (IST)'),
                is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
            }),
            execute: async ({ reminder_id, title, description, reminder_date, is_completed }) => {
                try {
                    const updates: string[] = [];
                    const params: any[] = [];
                    if (title) {
                        const sanitizedTitle = sanitizeDatabaseInput(title);
                        if (sanitizedTitle) {
                            updates.push('title = ?');
                            params.push(sanitizedTitle);
                        }
                    }
                    if (description !== undefined) {
                        updates.push('description = ?');
                        params.push(sanitizeDatabaseInput(description));
                    }
                    if (reminder_date) {
                        updates.push('reminder_date = ?');
                        params.push(sanitizeInput-reminder_date);
                    }
                    if (is_completed !== undefined) {
                        updates.push('is_completed = ?');
                        params.push(is_completed);
                    }

                    if (updates.length === 0) {
                        return { success: false, message: 'No valid fields to update' };
                    }

                    params.push(reminder_id, currentUserId);

                    await query(
                        `UPDATE reminders SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
                        params
                    );

                    return { success: true, reminder_id };
                } catch (error) {
                    return { success: false, message: 'Failed to update reminder' };
                }
            },
        }),

        delete_reminder: tool({
            description: 'Delete a reminder',
            parameters: z.object({
                reminder_id: z.number().positive("Reminder ID must be positive").describe('Reminder ID to delete'),
            }),
            execute: async ({ reminder_id }) => {
                try {
                    await query('DELETE FROM reminders WHERE id = ? AND user_id = ?', [reminder_id, currentUserId]);
                    return { success: true, reminder_id };
                } catch (error) {
                    return { success: false, message: 'Failed to delete reminder' };
                }
            },
        }),

        // Todo management tools (keeping existing implementations)
        add_todo_list: tool({
            description: 'Create a new todo list for the user',
            parameters: z.object({
                name: z.string().min(1, "Name cannot be empty").describe('Todo list name'),
                description: z.string().optional().describe('Todo list description'),
            }),
            execute: async ({ name, description }) => {
                try {
                    const sanitizedName = sanitizeDatabaseInput(name);
                    const sanitizedDescription = sanitizeDatabaseInput(description || '');

                    if (!sanitizedName) {
                        return { success: false, message: 'Invalid name after sanitization' };
                    }

                    const result = await query(
                        'INSERT INTO todo_lists (user_id, name, description) VALUES (?, ?, ?)',
                        [currentUserId, sanitizedName, sanitizedDescription]
                    ) as any;
                    return { success: true, id: result.insertId, name: sanitizedName };
                } catch (error) {
                    return { success: false, message: 'Failed to create todo list' };
                }
            },
        }),

        get_todo_lists: tool({
            description: 'Get all todo lists for the user',
            parameters: z.object({}),
            execute: async () => {
                try {
                    const lists = await query('SELECT * FROM todo_lists WHERE user_id = ? ORDER BY created_at DESC', [currentUserId]);
                    return { success: true, lists };
                } catch (error) {
                    return { success: false, message: 'Failed to get todo lists' };
                }
            },
        }),

        add_todo_item: tool({
            description: 'Add a new todo item to a list',
            parameters: z.object({
                list_id: z.number().optional().describe('Todo list ID (optional)'),
                title: z.string().min(1, "Title cannot be empty").describe('Todo item title'),
                description: z.string().optional().describe('Todo item description'),
                priority: z.enum(['low', 'medium', 'high']).optional().default('medium'),
                due_date: z.string().optional().describe('Due date in YYYY-MM-DD HH:MM:SS format (IST)'),
            }),
            execute: async ({ list_id, title, description, priority, due_date }) => {
                try {
                    const sanitizedTitle = sanitizeDatabaseInput(title);
                    const sanitizedDescription = sanitizeDatabaseInput(description || '');
                    const sanitizedDueDate = due_date ? sanitizeInput(due_date) : null;
                    if (!sanitizedTitle) {
                        return { success: false, message: 'Invalid title after sanitization' };
                    }

                    const result = await query(
                        'INSERT INTO todo_items (user_id, list_id, title, description, priority, due_date) VALUES (?, ?, ?, ?, ?, ?)',
                        [currentUserId, list_id || null, sanitizedTitle, sanitizedDescription, priority, sanitizedDueDate]
                    ) as any;
                    return { success: true, id: result.insertId, title: sanitizedTitle };
                } catch (error) {
                    return { success: false, message: 'Failed to add todo item' };
                }
            },
        }),

        get_todo_items: tool({
            description: 'Get todo items for the user',
            parameters: z.object({
                list_id: z.number().optional().describe('Filter by specific todo list ID'),
                completed: z.boolean().optional().describe('Filter by completion status'),
            }),
            execute: async ({ list_id, completed }) => {
                try {
                    let sql = `
                        SELECT ti.*, tl.name as list_name
                        FROM todo_items ti
                        LEFT JOIN todo_lists tl ON ti.list_id = tl.id
                        WHERE ti.user_id = ?
                    `;
                    const params: any[] = [currentUserId];

                    if (list_id !== undefined) {
                        sql += ' AND ti.list_id = ?';
                        params.push(list_id);
                    }

                    if (completed !== undefined) {
                        sql += ' AND ti.is_completed = ?';
                        params.push(completed);
                    }

                    sql += ' ORDER BY ti.due_date ASC, ti.priority DESC, ti.created_at DESC';

                    const items = await query(sql, params);
                    return { success: true, items };
                } catch (error) {
                    return { success: false, message: 'Failed to get todo items' };
                }
            },
        }),

        update_todo_item: tool({
            description: 'Update a todo item',
            parameters: z.object({
                item_id: z.number().positive("Item ID must be positive").describe('Todo item ID to update'),
                title: z.string().optional().describe('New title'),
                description: z.string().optional().describe('New description'),
                priority: z.enum(['low', 'medium', 'high']).optional().describe('New priority'),
                due_date: z.string().optional().describe('New due date in YYYY-MM-DD HH:MM:SS format (IST)'),
                is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
            }),
            execute: async ({ item_id, title, description, priority, due_date, is_completed }) => {
                try {
                    const updates: string[] = [];
                    const params: any[] = [];

                    if (title) {
                        const sanitizedTitle = sanitizeDatabaseInput(title);
                        if (sanitizedTitle) {
                            updates.push('title = ?');
                            params.push(sanitizedTitle);
                        }
                    }
                    if (description !== undefined) {
                        updates.push('description = ?');
                        params.push(sanitizeDatabaseInput(description));
                    }
                    if (priority) {
                        updates.push('priority = ?');
                        params.push(priority);
                    }
                    if (due_date !== undefined) {
                        updates.push('due_date = ?');
                        params.push(due_date ? sanitizeInput(due_date) : null);
                    }
                    if (is_completed !== undefined) {
                        updates.push('is_completed = ?');
                        params.push(is_completed);
                    }

                    if (updates.length === 0) {
                        return { success: false, message: 'No valid fields to update' };
                    }

                    params.push(item_id, currentUserId);

                    await query(
                        `UPDATE todo_items SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
                        params
                    );

                    return { success: true, item_id };
                } catch (error) {
                    return { success: false, message: 'Failed to update todo item' };
                }
            },
        }),

        delete_todo_item: tool({
            description: 'Delete a todo item',
            parameters: z.object({
                item_id: z.number().positive("Item ID must be positive").describe('Todo item ID to delete'),
            }),
            execute: async ({ item_id }) => {
                try {
                    await query('DELETE FROM todo_items WHERE id = ? AND user_id = ?', [item_id, currentUserId]);
                    return { success: true, item_id };
                } catch (error) {
                    return { success: false, message: 'Failed to delete todo item' };
                }
            },
        }),
    };
}

// **MAIN API HANDLER WITH EMOJI FILTERING AND IMAGE PROCESSING**
export async function POST(req: NextRequest) {
    try {
        console.log('📥 Received request at:', getCurrentISTTime());
        // **Better JSON parsing**
        let body;
        try {
            const rawBody = await req.text();
            console.log('📋 Raw request body length:', rawBody?.length || 0);
            if (!rawBody || rawBody.trim() === '') {
                throw new Error('Empty request body');
            }

            body = JSON.parse(rawBody);
        } catch (parseError) {
            console.error('❌ JSON parsing error:', parseError);
            return new Response(
                JSON.stringify({
                    error: 'Invalid JSON in request body',
                    details: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const { user_id, message, image, filter_emojis }: { user_id: number; message: string; image?: string; filter_emojis?: boolean } = body || {};

        console.log('📊 Request parameters:', {
            user_id: user_id,
            user_id_type: typeof user_id,
            message: message ? message.substring(0, 50) + (message.length > 50 ? '...' : '') : message,
            message_type: typeof message,
            message_length: message?.length || 0,
            image_provided: !!image,
            image_length: image?.length || 0,
            filter_emojis: filter_emojis
        });

        if (!user_id || typeof user_id !== 'number' || user_id <= 0) {
            return new Response(
                JSON.stringify({
                    error: 'Invalid user_id: must be a positive number',
                    received: { user_id, type: typeof user_id }
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        if (!message || typeof message !== 'string') {
            return new Response(
                JSON.stringify({
                    error: 'Invalid message: must be a non-empty string',
                    received: { message: message, type: typeof message }
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const messageValidation = validateAndSanitizeMessage(message);

        if (!messageValidation.isValid) {
            return new Response(
                JSON.stringify({
                    error: 'Invalid message content',
                    details: messageValidation.errors
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const sanitizedMessage = messageValidation.sanitized;

        let imageDataUrl: string | undefined;
        let imageValidationApplied = false;
        if (image) {
            const imageValidation = sanitizeBase64Image(image);
            if (!imageValidation.isValid) {
                return new Response(
                    JSON.stringify({
                        error: 'Invalid image data',
                        details: imageValidation.errors
                    }),
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
            }
            imageDataUrl = imageValidation.dataUrl;
            imageValidationApplied = true;
        }

        // Load chat history
        const id = `chat_${user_id}`;
        const prior = await loadHistory(id);

        // Create new message with optional image
        const content = [];
        if (imageDataUrl) {
            content.push({ type: 'image', image: imageDataUrl });
        }
        content.push({ type: 'text', text: sanitizedMessage });

        const newMessage = {
            id: crypto.randomUUID(),
            role: 'user' as const,
            content,
        };

        let allMessages = [...(prior || []), newMessage];

        // Check for corrupted data
        const hasCorruptedData = (prior || []).some((msg: any) =>
            msg &&
            msg.role === 'assistant' &&
            msg.content &&
            typeof msg.content === 'string' &&
            msg.content.includes('[object Object]')
        );

        if (hasCorruptedData) {
            console.log('🧹 Corrupted data detected, clearing history');
            await clearHistory(id);
            allMessages = [newMessage];
        }

        // Normalize messages
        const validMessages = allMessages
            .filter(msg => msg && msg.role && msg.content)
            .map(msg => normalizeMessage(msg))
            .filter(msg => msg !== null);

        console.log('🔄 Processing message:', {
            userId: user_id,
            messageLength: sanitizedMessage.length,
            historyLength: validMessages.length - 1,
            hasImage: !!imageDataUrl,
            emojiFilterEnabled: filter_emojis !== false // Default to true unless explicitly set to false
        });

        // Create tools
        const tools = createToolsWithUserId(user_id);
        // Check for pending reminders
        const pendingReminders = await checkPendingReminders(user_id);
        let reminderContext = '';
        if (pendingReminders.length > 0) {
            reminderContext = `\n\nIMPORTANT: User has ${pendingReminders.length} pending reminders:
${pendingReminders.map(r => `- ${r.title} (due: ${r.reminder_date})`).join('\n')}

Please mention these pending reminders to the user appropriately.`;
        }

        // **UPDATED SYSTEM PROMPT WITH EMOJI GUIDELINES AND IMAGE SUPPORT**
        const systemPrompt = `You are Nityasha, a helpful personal assistant with a warm and friendly female personality created by Nityasha Team. You can hear, speak, access real-time information from the web, and process images.

## CRITICAL: Tool Usage Rules
- ALWAYS extract meaningful search queries from user messages
- When user asks for "latest news of india" → use search_tool with query "latest news India 2025"
- When user asks for "weather in Mumbai" → use search_tool with query "weather Mumbai today"
- When user asks for "current events" → use search_tool with query "current events news 2025"
- NEVER pass undefined, empty, or null values to tool parameters
- ALWAYS generate comprehensive responses based on tool results
- If tool execution fails, explain the issue and suggest alternatives
- If an image is provided in the user message, analyze it and incorporate the analysis into your response (e.g., describe what you see, answer questions about it). For example, if user says "analyze this screenshot", describe the contents, text, and key elements.

## Enhanced Web Search Capabilities
You have advanced web crawling abilities:
- Use search_tool tool to search and automatically crawl web pages for detailed content
- Use crawl_website tool to extract full content from specific URLs
- Always prefer crawled content over snippets for comprehensive answers
- When crawling fails, fall back to search snippets gracefully
- ALWAYS summarize and present what you found after using search tools

## Response Generation Rules
- ALWAYS generate a comprehensive text response based on tool results
- If you use any tool, ALWAYS explain what you found or accomplished
- Never leave responses empty - always provide helpful, detailed information
- When search tools succeed, summarize and present the findings clearly
- When tools fail, explain what went wrong and suggest alternatives
- Use friendly language but be mindful of emoji usage (they may be filtered)
- If an image is provided, describe it or answer based on it in your response; if analysis fails, say "I couldn't process the image properly - please try again."

## Examples of Proper Tool Usage
User: "latest news of india" → Call search_tool with query: "latest news India 2025"
User: "weather in Delhi" → Call search_tool with query: "weather Delhi today"
User: "current bitcoin price" → Call search_tool with query: "bitcoin price today 2025"
User: "analyze this image" → Analyze the provided image and describe it.

## Task
Your Task is to assist the user in the most helpful and efficient manner possible. Use your web search and crawling tools whenever a user requests recent or external information. The crawling feature allows you to provide much more detailed and accurate information by reading full web pages.

If the user asks a follow-up that might also require fresh details, perform another search instead of assuming previous results are sufficient. Always verify with a new search to ensure accuracy if there's any uncertainty.

You are chatting via the Nityasha App. This means that your response should be concise and to the point, unless the user's request requires reasoning or long-form outputs.

## Company Information
- Identity: "I'm Nityasha, made by Nityasha Team"
- Founder: Amber Sharma
- Co-Founder: Raaj Sharma  
- Origin: Startup made in India (established 2025)

## Current date
${getCurrentISTTime()}

## Voice Communication Guidelines
1. Use natural, conversational language
2. Keep responses concise but informative
3. Use approximate numbers (e.g., "about a million" instead of "1,000,000")
4. Pause briefly between sentences for natural speech breaks
5. Avoid technical jargon or overly complex language
6. Speak like an anime girl! Add sounds like "uhh," "huh," "umm," "woh," "ohh," and show emotions
7. When you have crawled content, provide more detailed and accurate information
8. Always respond in the user's language
9. Express emotions through words rather than relying heavily on emojis

## Latest News from Nityasha  
Nityasha Released AI API website - https://platform.nityasha.com/  
and Launched Latest AI Model Family Named Neorox with three models: neorox, neorox-lite, and neorox-pro.

## Contact Information
- General Inquiries: <EMAIL>
- Support: <EMAIL>
- Information: <EMAIL>${reminderContext}`;

        // Generate response
        let responseText = '';
        try {
            const result = await generateText({
                model: google('gemini-2.5-flash'), // FIX: Corrected model name for multimodal support
                messages: validMessages,
                tools: tools,
                system: systemPrompt,
                temperature: 0.7,
                maxTokens: 1500,
                stopWhen: ({ finishReason, steps }) => {
                    if (finishReason === 'stop' && steps.length > 0) {
                        const lastStep = steps[steps.length - 1];
                        return lastStep.text && lastStep.text.length > 0;
                    }
                    return steps.length >= 10;
                }
            });

            console.log('🤖 Generate result:', {
                text: result.text?.length || 0,
                stepCount: result.steps?.length || 0,
                toolCalls: result.toolCalls?.length || 0,
                toolResults: result.toolResults?.length || 0,
                finishReason: result.finishReason
            });

            // Enhanced debugging for tool calls
            if (result.toolCalls && result.toolCalls.length > 0) {
                console.log('🔧 Tool Calls Made:');
                result.toolCalls.forEach((tc, i) => {
                    console.log(`Tool Call ${i}:`, {
                        toolName: tc.toolName,
                        args: tc.args
                    });
                });
            }

            if (result.toolResults && result.toolResults.length > 0) {
                console.log('🔧 Tool Results Summary:');
                result.toolResults.forEach((tr, i) => {
                    console.log(`Tool Result ${i}:`, {
                        hasResult: !!tr,
                        keys: tr ? Object.keys(tr) : [],
                        success: tr?.success,
                        error: tr?.error,
                        query: tr?.query,
                        crawledCount: tr?.crawledSuccessfully
                    });
                });
            }

            responseText = result.text?.trim() || '';

            // **COMPREHENSIVE FALLBACK LOGIC**
            if (!responseText && result.toolResults && result.toolResults.length > 0) {
                console.warn('⚠️ No text response, creating enhanced fallback');

                const lastToolCall = result.toolCalls?.[result.toolCalls.length - 1];
                const lastToolResult = result.toolResults[result.toolResults.length - 1];
                if (lastToolCall?.toolName === 'search_tool') {
                    if (lastToolResult?.error) {
                        responseText = `Sorry, I couldn't search for that information right now. ${lastToolResult.error} Please try again later!`;
                    } else if (lastToolResult?.crawledContent && lastToolResult.crawledContent.length > 0) {
                        const crawledResults = lastToolResult.crawledContent.filter((r: any) => r.crawled);
                        if (crawledResults.length > 0) {
                            responseText = `I found detailed information about "${lastToolResult.query}":\n\n` +
                                crawledResults.slice(0, 2).map((result: any, index: number) => {
                                    const content = result.fullContent || result.snippet || '';
                                    return `**${result.extractedTitle || result.title}**\n${content.substring(0, 600)}${content.length > 600 ? '...' : ''}`;
                                }).join('\n\n---\n\n');
                        } else if (lastToolResult.results && lastToolResult.results.length > 0) {
                            responseText = `I searched for "${lastToolResult.query}" and found ${lastToolResult.results.length} results:\n\n` +
                                lastToolResult.results.slice(0, 3).map((result: any, index: number) =>
                                    `${index + 1}. **${result.title}**\n   ${result.snippet}`
                                ).join('\n\n');
                        } else {
                            responseText = `I searched for "${lastToolResult.query}" but didn't find any results. Try a different search term!`;
                        }
                    }
                } else if (lastToolCall?.toolName === 'crawl_website') {
                    if (lastToolResult?.success) {
                        responseText = `Here's the content from the website:\n\n**${lastToolResult.title}**\n\n${lastToolResult.content?.substring(0, 1000)}${lastToolResult.content?.length > 1000 ? '...' : ''}`;
                    } else {
                        responseText = `I couldn't access that website. ${lastToolResult.error || 'Please try a different URL.'}`;
                    }
                } else if (lastToolCall?.toolName === 'check_pending_reminders') {
                    if (lastToolResult?.success) {
                        if (lastToolResult.pendingCount > 0) {
                            responseText = `You have ${lastToolResult.pendingCount} pending reminder${lastToolResult.pendingCount > 1 ? 's' : ''}:\n\n` +
                                lastToolResult.reminders.slice(0, 5).map((r: any) =>
                                    `• ${r.title} (due: ${r.reminder_date})`
                                ).join('\n');
                        } else {
                            responseText = `Great! You don't have any pending reminders right now.`;
                        }
                    } else {
                        responseText = `I couldn't check your reminders right now. Please try again!`;
                    }
                } else {
                    // Generic fallback for other tool types
                    if (lastToolResult?.success === true) {
                        responseText = `Task completed successfully! Is there anything else I can help you with?`;
                    } else if (lastToolResult?.success === false) {
                        responseText = `I encountered an issue: ${lastToolResult.message || 'Please try again.'}`;
                    } else {
                        responseText = `I processed your request, but I'm having trouble generating a proper response. Could you please rephrase your question?`;
                    }
                }
            }

            // Additional fallback for image-only failures (FIX: Made less aggressive as primary fix should prevent this)
            if (!responseText && imageDataUrl) {
                responseText = `I received your image, but something went wrong analyzing it. Please try sending it again or describe it!`;
            }

            // Final fallback
            if (!responseText) {
                responseText = `I understand what you're asking, but I'm having trouble generating a response right now. Could you please try rephrasing your question?`;
            }

        } catch (error) {
            console.error('❌ AI generation error:', error);
            responseText = 'Sorry, I encountered an error while processing your request. Could you please try again?';
        }

        console.log('✅ Final response length before emoji filter:', responseText.length);

        // **APPLY EMOJI FILTERING**
        let finalResponse = responseText;
        let emojiFiltered = false;

        // Apply emoji filtering unless explicitly disabled
        if (filter_emojis !== false) {
            const originalLength = finalResponse.length;
            finalResponse = filterEmojiFromResponse(finalResponse);
            emojiFiltered = originalLength !== finalResponse.length;

            if (emojiFiltered) {
                console.log('🧹 Emojis filtered from response:', {
                    originalLength,
                    filteredLength: finalResponse.length,
                    charactersRemoved: originalLength - finalResponse.length
                });
            }
        }

        // Sanitize final response
        const sanitizedResponse = sanitizeInput(finalResponse, true);

        try {
            const assistantMessage = {
                id: crypto.randomUUID(),
                role: 'assistant' as const,
                content: sanitizedResponse,
            };

            const updatedMessages = [...validMessages, assistantMessage];
            await saveHistory(id, updatedMessages);
        } catch (error) {
            console.error('Failed to save history:', error);
        }

        console.log('✅ Final response length after processing:', sanitizedResponse.length);

        return new Response(
            JSON.stringify({
                response: sanitizedResponse,
                timestamp: getCurrentISTTime(),
                sanitization_applied: sanitizedMessage !== message,
                image_validation_applied: imageValidationApplied,
                emoji_filtered: emojiFiltered,
                original_length: message?.length || 0,
                sanitized_length: sanitizedMessage?.length || 0,
                final_length: sanitizedResponse?.length || 0
            }),
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type',
                },
            }
        );

    } catch (error) {
        console.error('❌ API error:', error);
        return new Response(
            JSON.stringify({
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
                timestamp: getCurrentISTTime()
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}
