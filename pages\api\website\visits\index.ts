import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';
import axios from 'axios'; // Axios for making API calls

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      // Parse User-Agent to determine device type
      const deviceType = req.headers['user-agent']?.toLowerCase().includes('mobile') ? 'mobile' : 'desktop';

      // Get IP address from headers (fallback to a default IP in dev)
      const ipAddress = 
        req.headers['x-forwarded-for']?.toString().split(',')[0] || 
        req.socket.remoteAddress || 
        '*******'; // Fallback to Google's IP for dev testing

      // Fetch geolocation data using ip-api (free geolocation API)
      const { data: location } = await axios.get(`http://ip-api.com/json/${ipAddress}`);

      // Save traffic data, including location
      const trafficData = await prisma.traffic.create({
        data: {
          desktop: deviceType === 'desktop',
          mobile: deviceType === 'mobile',
          date: new Date(),
          ip: ipAddress,
          country: location.country || 'Unknown',
          city: location.city || 'Unknown',
        },
      });

      res.status(200).json({ message: 'Visit recorded', data: trafficData });
    } catch (error) {
      console.error('Error recording traffic:', error);
      res.status(500).json({ message: 'Error recording visit', error });
    }
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
