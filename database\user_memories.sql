-- User Memories Table
CREATE TABLE user_memories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    `key` VARCHAR(255) NOT NULL,  -- e.g., "favorite_color"
    value TEXT NOT NULL,           -- e.g., "blue"
    description TEXT,              -- Optional context
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_key (user_id, `key`)  -- Composite index for faster lookups
);

-- Sample data (optional)
INSERT INTO user_memories (user_id, `key`, value, description) VALUES 
(1, 'favorite_color', 'blue', 'User\'s preferred color'),
(1, 'preferred_language', 'English', 'User\'s language preference'),
(1, 'location', 'New York', 'User\'s last known location');
