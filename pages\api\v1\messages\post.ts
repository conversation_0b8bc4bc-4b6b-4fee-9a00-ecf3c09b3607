import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '@/lib/db'; // Adjust the import based on your file structure

// Define the type for the query result
interface QueryResult {
    insertId: number;
}

// Handler for creating a new message
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { message_text, recipient_id, sender_id } = req.body;

        // Basic validation
        if (!message_text || !recipient_id || !sender_id) {
            return res.status(400).json({ error: 'All fields are required' });
        }

        try {
            // Insert the message into the database
            const result = await query(
                'INSERT INTO messages (message_text, recipient_id, sender_id) VALUES (?, ?, ?)',
                [message_text, recipient_id, sender_id]
            ) as QueryResult; // Type assertion here

            return res.status(201).json({ id: result.insertId, message_text, recipient_id, sender_id });
        } catch (error) {
            console.error('Error inserting message:', error);
            return res.status(500).json({ error: 'Failed to create message' });
        }
    } else {
        // Handle any other HTTP method
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
