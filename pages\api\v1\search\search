// pages/api/v1/search/search.ts

import type { NextApiRequest, NextApiResponse } from 'next';
import { query } from '../../../../lib/db'; // Ensure you have the `query` function defined to connect to MySQL

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const { q } = req.query;

    // Validate the query
    if (!q || typeof q !== 'string') {
        return res.status(400).json({ message: 'Invalid search query' });
    }

    try {
        // SQL query to search for records where the title matches the search query
        const sql = `
            SELECT id, user_id, title, room_name, agora_channel_id, thumbnail, created_at 
            FROM live
            WHERE title LIKE ? OR room_name LIKE ?
        `;
        const results = await query(sql, [`%${q}%`, `%${q}%`]);

        if (results.length === 0) {
            return res.status(404).json({ message: 'No results found' });
        }

        res.status(200).json(results);
    } catch (error) {
        console.error('Error executing search query:', error);
        res.status(500).json({ message: 'Server error', error });
    }
}
