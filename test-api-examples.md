# AI API with <PERSON><PERSON><PERSON> and Todo Lists - Usage Examples

## Setup
First, run the MySQL schema:
```sql
-- Run the database/schema.sql file in your MySQL database
```

## API Endpoint
`POST /api/ai/human`

## Request Format
```json
{
  "user_id": 12,
  "message": "your message here"
}
```

## Examples

### 1. Weather Query
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "What is the weather in Guna, Madhya Pradesh?"
  }'
```

### 2. Add Reminder
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Add a reminder to call doctor tomorrow at 2 PM"
  }'
```

### 3. Get My Reminders
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Show me all my reminders"
  }'
```

### 4. Create Todo List
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Create a new todo list called Shopping List"
  }'
```

### 5. Add Todo Item
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Add buy milk to my shopping list with high priority"
  }'
```

### 6. Get Todo Items
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Show me all my todo items"
  }'
```

### 7. Mark Todo as Complete
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Mark todo item 1 as completed"
  }'
```

### 8. Update Reminder
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Update reminder 1 to tomorrow at 3 PM instead"
  }'
```

### 9. Delete Todo Item
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Delete todo item 2"
  }'
```

### 10. Complex Query
```bash
curl -X POST http://localhost:3000/api/ai/human \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 12,
    "message": "Show me my pending reminders and incomplete todo items for today"
  }'
```

## Available Tools

### Reminder Tools:
- **add_reminder**: Add new reminder
- **get_reminders**: Get all reminders (can filter by completion status)
- **update_reminder**: Update existing reminder
- **delete_reminder**: Delete reminder

### Todo List Tools:
- **add_todo_list**: Create new todo list
- **get_todo_lists**: Get all todo lists
- **add_todo_item**: Add item to todo list
- **get_todo_items**: Get todo items (can filter by list or completion)
- **update_todo_item**: Update todo item
- **delete_todo_item**: Delete todo item

### Weather Tool:
- **get_current_weather**: Get weather for location

## Natural Language Examples

The AI understands natural language, so you can say things like:
- "Remind me to take medicine at 8 AM tomorrow"
- "Add groceries to my shopping list"
- "What's the weather like in Mumbai?"
- "Show me my completed tasks"
- "Mark my first reminder as done"
- "Create a work todo list"
- "Add finish report to work list with high priority due Friday"

## Response Format
```json
{
  "response": "AI response with tool results"
}
```
